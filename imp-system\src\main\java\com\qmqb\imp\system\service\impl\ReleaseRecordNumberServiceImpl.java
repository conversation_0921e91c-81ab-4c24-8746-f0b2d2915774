package com.qmqb.imp.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.constant.CommConstants;
import com.qmqb.imp.common.enums.RedisKey;
import com.qmqb.imp.common.utils.redis.RedisUtils;
import com.qmqb.imp.system.domain.ReleaseRecord;
import com.qmqb.imp.system.mapper.ReleaseRecordMapper;
import com.qmqb.imp.system.service.IReleaseRecordNumberService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 发布版本记录流水号服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReleaseRecordNumberServiceImpl implements IReleaseRecordNumberService {

    private final ReleaseRecordMapper releaseRecordMapper;

    @Override
    public int getNextSerialNumber(String dateStr) {
        try {
            // Redis中流水号的key
            RedisKey redisKey = RedisKey.RELEASE_SERIAL;
            String key = redisKey.getRedisKey(dateStr);

            // 尝试从Redis中获取当前流水号
            Integer currentSerial = RedisUtils.getCacheObject(key);
            if (currentSerial != null && currentSerial > 0) {
                // Redis中存在，递增并更新Redis
                int nextSerial = currentSerial + 1;
                RedisUtils.setCacheObject(key, nextSerial, redisKey.getTtl());
                log.info("从Redis获取流水号: {}, 日期: {}, 下一个: {}", currentSerial, dateStr, nextSerial);
                return currentSerial;
            }

            // Redis中不存在，查询数据库
            int nextSerial = getNextSerialFromDb(dateStr);

            // 将结果存入Redis，设置过期时间
            RedisUtils.setCacheObject(key, nextSerial + 1, redisKey.getTtl());
            log.info("从数据库获取流水号: {}, 日期: {}", nextSerial, dateStr);

            return nextSerial;
        } catch (Exception e) {
            log.error("获取下一个流水号失败", e);
            throw new ServiceException("获取下一个流水号失败");
        }
    }

    @Override
    public int getBatchSerialNumber(String dateStr, int count) {
        if (count <= 0) {
            return getNextSerialNumber(dateStr);
        }

        try {
            // Redis中流水号的key
            RedisKey redisKey = RedisKey.RELEASE_SERIAL;
            String key = redisKey.getRedisKey(dateStr);

            // 尝试从Redis中获取当前流水号
            Integer currentSerial = RedisUtils.getCacheObject(key);
            if (currentSerial != null && currentSerial > 0) {
                // Redis中存在，一次性增加count个并更新Redisgit worktree list
                int nextSerial = currentSerial + count;
                RedisUtils.setCacheObject(key, nextSerial, redisKey.getTtl());
                log.info("批量从Redis获取{}个流水号, 起始: {}, 日期: {}", count, currentSerial, dateStr);
                return currentSerial;
            }

            // Redis中不存在，查询数据库
            int nextSerial = getNextSerialFromDb(dateStr);

            // 将结果存入Redis，设置过期时间
            RedisUtils.setCacheObject(key, nextSerial + count, redisKey.getTtl());
            log.info("批量从数据库获取{}个流水号, 起始: {}, 日期: {}", count, nextSerial, dateStr);

            return nextSerial;
        } catch (Exception e) {
            log.error("批量获取流水号失败", e);
            throw new ServiceException("批量获取流水号失败");
        }
    }

    @Override
    public String generateResultCode(String dateStr, int serialNumber) {
        return "PR" + dateStr + String.format("%04d", serialNumber);
    }

    /**
     * 从数据库获取下一个流水号
     */
    private int getNextSerialFromDb(String dateStr) {
        // 查询当天最大的流水号
        String likePattern = "PR" + dateStr + "%";
        List<ReleaseRecord> todayRecords = releaseRecordMapper.selectList(
            new LambdaQueryWrapper<ReleaseRecord>()
                .like(ReleaseRecord::getResultCode, likePattern)
                .orderByDesc(ReleaseRecord::getResultCode)
                .last("LIMIT 1")
        );
        // 默认从1开始
        int nextSerial = 1;

        if (CollUtil.isNotEmpty(todayRecords)) {
            String maxCode = todayRecords.get(0).getResultCode();
            // 提取流水号部分（最后4位）
            if (maxCode.length() >= CommConstants.CommonVal.FOUR) {
                String serialStr = maxCode.substring(maxCode.length() - 4);
                try {
                    int currentMax = Integer.parseInt(serialStr);
                    // 数据库中最大值加1
                    nextSerial = currentMax + 1;
                } catch (NumberFormatException e) {
                    log.warn("解析流水号失败: {}", serialStr);
                    throw new ServiceException("解析流水号失败");
                }
            }
        }

        return nextSerial;
    }
}
