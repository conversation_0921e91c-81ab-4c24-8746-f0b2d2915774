package com.qmqb.imp.system.domain.bo;

import com.qmqb.imp.system.domain.ProjectTechnology;
import lombok.Data;

import java.util.List;

/**
 * 代码库相关技术栈业务对象 tb_project_technology按类型分类
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
public class ProjectTechnologyGroupBO {

    /**
     * ui
     */
    private List<ProjectTechnology> ui;

    /**
     * 后端
     */
    private List<ProjectTechnology> back;

    /**
     * android
     */
    private List<ProjectTechnology> android;

    /**
     * ios
     */
    private List<ProjectTechnology> ios;
}
