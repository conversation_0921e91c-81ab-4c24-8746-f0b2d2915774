package com.qmqb.imp.system.domain.bo;

import com.qmqb.imp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.*;


/**
 * 代码库相关技术栈业务对象 tb_project_technology
 *
 * <AUTHOR>
 * @date 2025-08-14
 */

@Data
public class ProjectTechnologyBo {

    /**
     * 主键
     */
    private Long id;

    /**
     * 项目ID
     */
    private Long pId;

    /**
     * 框架类型 ui/back/android/ios
     */
    private String type;

    /**
     * 框架
     */
    private String framework;


}
