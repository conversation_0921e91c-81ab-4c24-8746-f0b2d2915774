package com.qmqb.imp.test;

import cn.hutool.core.date.SystemClock;
import cn.hutool.core.lang.Dict;
import cn.hutool.crypto.SignUtil;
import cn.hutool.crypto.digest.DigestAlgorithm;
import cn.hutool.http.HttpUtil;
import com.hzed.structure.common.constant.StringPool;
import com.hzed.structure.common.util.IdUtil;
import com.qmqb.imp.common.utils.JsonUtils;

/**
 * 单元测试案例
 *
 * <AUTHOR> Li
 */
public class BaseTest {

    private static final String SIGN_APP_SECRET = "&app_secret=";
    private static final String APP_SECRET = "43fd5ffdcdb47952d3477582d265661a7bc38c01";

    public static void main(String[] args) {
        // 邮件
        String email = "        {\n" +
            "            \"sign\": \"2fe5a5d97f4dd9cb9199d2c581e0e0cb\",\n" +
            "            \"app_key\": \"impEOdzG2yK\",\n" +
            "            \"timestamp\": 1675153640816,\n" +
            "            \"terminal_no\": null,\n" +
            "            \"third_task_no\": \"1620337926749245442\",\n" +
            "            \"message_content\": \"{\\\"from\\\":null,\\\"to\\\":[\\\"<EMAIL>\\\"],\\\"cc\\\":null,\\\"bcc\\\":null,\\\"subject\\\":\\\"项目预警\\\",\\\"text\\\":\\\"项目结束日期已过且没有关闭且逾期10个工作日\\\"}\",\n" +
            "            \"channel_type\": \"3\"\n" +
            "        }";
        Dict params = JsonUtils.parseMap(email);
        params.remove("sign");
        params.put("timestamp", SystemClock.now());
        params.put("third_task_no", IdUtil.getId());
        String resultSign = SignUtil.signParams(DigestAlgorithm.MD5, params, StringPool.AMPERSAND, StringPool.EQUALS, true, SIGN_APP_SECRET + APP_SECRET);
        params.put("sign", resultSign);
        String body = HttpUtil.createPost("http://message.qmwallet.vip/message-center-api/v1/message/sendBase").body(JsonUtils.toJsonString(params)).execute().body();
        System.out.println(body);
        // 钉钉通知
//        String ding = "{\n" +
//            "    \"sign\": \"3e44800bf00892d02927efade5c1be07\",\n" +
//            "    \"app_key\": \"impEOdzG2yK\",\n" +
//            "    \"timestamp\": 1675154545587,\n" +
//            "    \"terminal_no\": null,\n" +
//            "    \"third_task_no\": \"1620341721633869825\",\n" +
//            "    \"message_content\": \"{\\\"msg\\\":{\\\"msgtype\\\":\\\"text\\\",\\\"text\\\":{\\\"content\\\":\\\"项目结束日期已过且没有关闭且逾期10个工作日\\\"}},\\\"userid_list\\\":\\\"0515683219689916\\\",\\\"dept_id_list\\\":null,\\\"to_all_user\\\":null}\",\n" +
//            "    \"channel_type\": \"4\"\n" +
//            "}";
//        Dict params = JsonUtils.parseMap(ding);
//        params.remove("sign");
//        params.put("timestamp", SystemClock.now());
//        params.put("third_task_no", IdUtil.getId());
//        String resultSign = SignUtil.signParams(DigestAlgorithm.MD5, params, StringPool.AMPERSAND, StringPool.EQUALS, true, SIGN_APP_SECRET + appSecret);
//        params.put("sign", resultSign);
//        String body = HttpUtil.createPost("http://10.10.20.198:9801/message-center-api/v1/message/sendBase").body(JsonUtils.toJsonString(params)).execute().body();
//        System.out.println(body);
    }

}
