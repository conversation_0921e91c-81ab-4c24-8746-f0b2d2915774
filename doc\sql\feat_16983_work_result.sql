alter table tb_work_stat
    add work_wait_task_count int default 0 null comment '未开始的任务' after work_doing_task_count;

INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1960243329449340929, '创建禅道任务API参数', 'zentao_task_config', '0', 'admin', '2025-08-26 15:30:04', 'admin', '2025-08-26 15:30:04', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1960243410068058113, 0, 'task_story', '16904', 'zentao_task_config', NULL, 'default', 'N', '0', 'admin', '2025-08-26 15:30:23', 'admin', '2025-08-26 15:31:32', '关联需求id');
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1960243778361503745, 0, 'task_module', '2183', 'zentao_task_config', NULL, 'default', 'N', '0', 'admin', '2025-08-26 15:31:51', 'admin', '2025-08-26 15:31:51', '关联模块id');
