/*
 * All content copyright Terracotta, Inc., unless otherwise indicated. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy
 * of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 *
 */

package com.xxl.job.admin.core.cron;

import java.io.Serializable;
import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Locale;
import java.util.Map;
import java.util.SortedSet;
import java.util.StringTokenizer;
import java.util.TimeZone;
import java.util.TreeSet;

/**
 * Provides a parser and evaluator for unix-like cron expressions. Cron
 * expressions provide the ability to specify complex time combinations such as
 * &quot;At 8:00am every Monday through Friday&quot; or &quot;At 1:30am every
 * last Friday of the month&quot;.
 * <P>
 * Cron expressions are comprised of 6 required fields and one optional field
 * separated by white space. The fields respectively are described as follows:
 *
 * <table cellspacing="8">
 * <tr>
 * <th align="left">Field Name</th>
 * <th align="left">&nbsp;</th>
 * <th align="left">Allowed Values</th>
 * <th align="left">&nbsp;</th>
 * <th align="left">Allowed Special Characters</th>
 * </tr>
 * <tr>
 * <td align="left"><code>Seconds</code></td>
 * <td align="left">&nbsp;</th>
 * <td align="left"><code>0-59</code></td>
 * <td align="left">&nbsp;</th>
 * <td align="left"><code>, - * /</code></td>
 * </tr>
 * <tr>
 * <td align="left"><code>Minutes</code></td>
 * <td align="left">&nbsp;</th>
 * <td align="left"><code>0-59</code></td>
 * <td align="left">&nbsp;</th>
 * <td align="left"><code>, - * /</code></td>
 * </tr>
 * <tr>
 * <td align="left"><code>Hours</code></td>
 * <td align="left">&nbsp;</th>
 * <td align="left"><code>0-23</code></td>
 * <td align="left">&nbsp;</th>
 * <td align="left"><code>, - * /</code></td>
 * </tr>
 * <tr>
 * <td align="left"><code>Day-of-month</code></td>
 * <td align="left">&nbsp;</th>
 * <td align="left"><code>1-31</code></td>
 * <td align="left">&nbsp;</th>
 * <td align="left"><code>, - * ? / L W</code></td>
 * </tr>
 * <tr>
 * <td align="left"><code>Month</code></td>
 * <td align="left">&nbsp;</th>
 * <td align="left"><code>0-11 or JAN-DEC</code></td>
 * <td align="left">&nbsp;</th>
 * <td align="left"><code>, - * /</code></td>
 * </tr>
 * <tr>
 * <td align="left"><code>Day-of-Week</code></td>
 * <td align="left">&nbsp;</th>
 * <td align="left"><code>1-7 or SUN-SAT</code></td>
 * <td align="left">&nbsp;</th>
 * <td align="left"><code>, - * ? / L #</code></td>
 * </tr>
 * <tr>
 * <td align="left"><code>Year (Optional)</code></td>
 * <td align="left">&nbsp;</th>
 * <td align="left"><code>empty, 1970-2199</code></td>
 * <td align="left">&nbsp;</th>
 * <td align="left"><code>, - * /</code></td>
 * </tr>
 * </table>
 * <P>
 * The '*' character is used to specify all values. For example, &quot;*&quot;
 * in the minute field means &quot;every minute&quot;.
 * <P>
 * The '?' character is allowed for the day-of-month and day-of-week fields. It
 * is used to specify 'no specific value'. This is useful when you need to
 * specify something in one of the two fields, but not the other.
 * <P>
 * The '-' character is used to specify ranges For example &quot;10-12&quot; in
 * the hour field means &quot;the hours 10, 11 and 12&quot;.
 * <P>
 * The ',' character is used to specify additional values. For example
 * &quot;MON,WED,FRI&quot; in the day-of-week field means &quot;the days Monday,
 * Wednesday, and Friday&quot;.
 * <P>
 * The '/' character is used to specify increments. For example &quot;0/15&quot;
 * in the seconds field means &quot;the seconds 0, 15, 30, and 45&quot;. And
 * &quot;5/15&quot; in the seconds field means &quot;the seconds 5, 20, 35, and
 * 50&quot;.  Specifying '*' before the  '/' is equivalent to specifying 0 is
 * the value to start with. Essentially, for each field in the expression, there
 * is a set of numbers that can be turned on or off. For seconds and minutes,
 * the numbers range from 0 to 59. For hours 0 to 23, for days of the month 0 to
 * 31, and for months 0 to 11 (JAN to DEC). The &quot;/&quot; character simply helps you turn
 * on every &quot;nth&quot; value in the given set. Thus &quot;7/6&quot; in the
 * month field only turns on month &quot;7&quot;, it does NOT mean every 6th
 * month, please note that subtlety.
 * <P>
 * The 'L' character is allowed for the day-of-month and day-of-week fields.
 * This character is short-hand for &quot;last&quot;, but it has different
 * meaning in each of the two fields. For example, the value &quot;L&quot; in
 * the day-of-month field means &quot;the last day of the month&quot; - day 31
 * for January, day 28 for February on non-leap years. If used in the
 * day-of-week field by itself, it simply means &quot;7&quot; or
 * &quot;SAT&quot;. But if used in the day-of-week field after another value, it
 * means &quot;the last xxx day of the month&quot; - for example &quot;6L&quot;
 * means &quot;the last friday of the month&quot;. You can also specify an offset
 * from the last day of the month, such as "L-3" which would mean the third-to-last
 * day of the calendar month. <i>When using the 'L' option, it is important not to
 * specify lists, or ranges of values, as you'll get confusing/unexpected results.</i>
 * <P>
 * The 'W' character is allowed for the day-of-month field.  This character
 * is used to specify the weekday (Monday-Friday) nearest the given day.  As an
 * example, if you were to specify &quot;15W&quot; as the value for the
 * day-of-month field, the meaning is: &quot;the nearest weekday to the 15th of
 * the month&quot;. So if the 15th is a Saturday, the trigger will fire on
 * Friday the 14th. If the 15th is a Sunday, the trigger will fire on Monday the
 * 16th. If the 15th is a Tuesday, then it will fire on Tuesday the 15th.
 * However if you specify &quot;1W&quot; as the value for day-of-month, and the
 * 1st is a Saturday, the trigger will fire on Monday the 3rd, as it will not
 * 'jump' over the boundary of a month's days.  The 'W' character can only be
 * specified when the day-of-month is a single day, not a range or list of days.
 * <P>
 * The 'L' and 'W' characters can also be combined for the day-of-month
 * expression to yield 'LW', which translates to &quot;last weekday of the
 * month&quot;.
 * <P>
 * The '#' character is allowed for the day-of-week field. This character is
 * used to specify &quot;the nth&quot; XXX day of the month. For example, the
 * value of &quot;6#3&quot; in the day-of-week field means the third Friday of
 * the month (day 6 = Friday and &quot;#3&quot; = the 3rd one in the month).
 * Other examples: &quot;2#1&quot; = the first Monday of the month and
 * &quot;4#5&quot; = the fifth Wednesday of the month. Note that if you specify
 * &quot;#5&quot; and there is not 5 of the given day-of-week in the month, then
 * no firing will occur that month.  If the '#' character is used, there can
 * only be one expression in the day-of-week field (&quot;3#1,6#3&quot; is
 * not valid, since there are two expressions).
 * <P>
 * <!--The 'C' character is allowed for the day-of-month and day-of-week fields.
 * This character is short-hand for "calendar". This means values are
 * calculated against the associated calendar, if any. If no calendar is
 * associated, then it is equivalent to having an all-inclusive calendar. A
 * value of "5C" in the day-of-month field means "the first day included by the
 * calendar on or after the 5th". A value of "1C" in the day-of-week field
 * means "the first day included by the calendar on or after Sunday".-->
 * <P>
 * The legal characters and the names of months and days of the week are not
 * case sensitive.
 *
 * <p>
 * <b>NOTES:</b>
 * <ul>
 * <li>Support for specifying both a day-of-week and a day-of-month value is
 * not complete (you'll need to use the '?' character in one of these fields).
 * </li>
 * <li>Overflowing ranges is supported - that is, having a larger number on
 * the left hand side than the right. You might do 22-2 to catch 10 o'clock
 * at night until 2 o'clock in the morning, or you might have NOV-FEB. It is
 * very important to note that overuse of overflowing ranges creates ranges
 * that don't make sense and no effort has been made to determine which
 * interpretation CronExpression chooses. An example would be
 * "0 0 14-6 ? * FRI-MON". </li>
 * </ul>
 * </p>
 *
 * <AUTHOR> Jambula, James House
 * <AUTHOR> from Mads Henderson
 * <AUTHOR> from CronTrigger to CronExpression by Aaron Craven
 * <p>
 * Borrowed from quartz v2.3.1
 */
public final class CronExpression implements Serializable, Cloneable {

    private static final long serialVersionUID = 12423409423L;

    protected static final int SECOND = 0;
    protected static final int MINUTE = 1;
    protected static final int HOUR = 2;
    protected static final int DAY_OF_MONTH = 3;
    protected static final int MONTH = 4;
    protected static final int DAY_OF_WEEK = 5;
    protected static final int YEAR = 6;
    protected static final int ALL_SPEC_INT = 99;
    protected static final int NO_SPEC_INT = 98;
    protected static final Integer ALL_SPEC = ALL_SPEC_INT;
    protected static final Integer NO_SPEC = NO_SPEC_INT;

    private static final long MILLISECONDS_IN_SECOND = 1000L;
    private static final int MAX_SECOND_OR_MINUTE = 59;
    private static final int MAX_HOUR = 23;
    private static final int MAX_DAY_OF_MONTH = 31;
    private static final int MAX_MONTH = 12;
    private static final int MAX_DAY_OF_WEEK = 7;
    private static final int DAYS_IN_WEEK = 7;
    private static final int HOURS_IN_DAY = 24;
    private static final int MAX_SINGLE_DIGIT = 10;
    private static final int SINGLE_DIGIT_THRESHOLD = 10;
    private static final int MAX_LAST_DAY_OFFSET = 30;
    private static final int MAX_NTH_DAY_OF_WEEK = 5;
    private static final int MIN_NTH_DAY_OF_WEEK = 1;
    private static final int SUBSTRING_LENGTH = 3;
    private static final int YEAR_2999 = 2999;
    private static final int YEAR_1970 = 1970;
    private static final int MAGIC_YEAR_3333 = 3333;

    private static final int FEBRUARY_LEAP_DAYS = 29;
    private static final int FEBRUARY_NORMAL_DAYS = 28;
    private static final int APRIL_JUNE_SEPT_NOV_DAYS = 30;

    private static final int LEAP_YEAR_DIVISOR_4 = 4;
    private static final int LEAP_YEAR_DIVISOR_100 = 100;
    private static final int LEAP_YEAR_DIVISOR_400 = 400;

    private static final String LAST_DAY_STRING = "L";
    private static final String LAST_WEEKDAY_STRING = "LW";
    private static final String LAST_DAY_OFFSET_PATTERN = "^L-[0-9]*[W]?";
    private static final char CHAR_A = 'A';
    private static final char CHAR_Z = 'Z';
    private static final char CHAR_ZERO = '0';
    private static final char CHAR_NINE = '9';
    private static final char NO_SPECIFIC_VALUE_CHAR = '?';
    private static final char ALL_VALUES_CHAR = '*';
    private static final char INCREMENT_CHAR = '/';
    private static final char WEEKDAY_CHAR = 'W';
    private static final char NTH_DAY_CHAR = '#';
    private static final char RANGE_CHAR = '-';
    private static final char SPACE_CHAR = ' ';
    private static final char TAB_CHAR = '\t';
    private static final char LAST_DAY_CHAR = 'L';
    private static final String COMMA_STRING = ",";

    private static final int MIN_SECOND_OR_MINUTE = 0;
    private static final int MIN_HOUR = 0;
    private static final int MIN_DAY_OF_MONTH = 1;
    private static final int MIN_MONTH = 1;
    private static final int MIN_DAY_OF_WEEK = 1;

    private static final int SECONDS_MAX = 60;
    private static final int MINUTES_MAX = 60;
    private static final int HOURS_MAX = 24;
    private static final int MONTHS_MAX = 12;
    private static final int DAYS_OF_WEEK_MAX = 7;
    private static final int DAYS_OF_MONTH_MAX = 31;

    protected static final Map<String, Integer> MONTH_MAP = new HashMap<String, Integer>(20);
    protected static final Map<String, Integer> DAY_MAP = new HashMap<String, Integer>(60);

    static {
        MONTH_MAP.put("JAN", 0);
        MONTH_MAP.put("FEB", 1);
        MONTH_MAP.put("MAR", 2);
        MONTH_MAP.put("APR", 3);
        MONTH_MAP.put("MAY", 4);
        MONTH_MAP.put("JUN", 5);
        MONTH_MAP.put("JUL", 6);
        MONTH_MAP.put("AUG", 7);
        MONTH_MAP.put("SEP", 8);
        MONTH_MAP.put("OCT", 9);
        MONTH_MAP.put("NOV", 10);
        MONTH_MAP.put("DEC", 11);

        DAY_MAP.put("SUN", 1);
        DAY_MAP.put("MON", 2);
        DAY_MAP.put("TUE", 3);
        DAY_MAP.put("WED", 4);
        DAY_MAP.put("THU", 5);
        DAY_MAP.put("FRI", 6);
        DAY_MAP.put("SAT", 7);
    }

    private final String cronExpression;
    private TimeZone timeZone = null;
    protected transient TreeSet<Integer> seconds;
    protected transient TreeSet<Integer> minutes;
    protected transient TreeSet<Integer> hours;
    protected transient TreeSet<Integer> daysOfMonth;
    protected transient TreeSet<Integer> months;
    protected transient TreeSet<Integer> daysOfWeek;
    protected transient TreeSet<Integer> years;

    protected transient boolean lastdayOfWeek = false;
    protected transient int nthdayOfWeek = 0;
    protected transient boolean lastdayOfMonth = false;
    protected transient boolean nearestWeekday = false;
    protected transient int lastdayOffset = 0;
    protected transient boolean expressionParsed = false;

    public static final int MAX_YEAR = Calendar.getInstance().get(Calendar.YEAR) + 100;

    /**
     * Constructs a new <CODE>CronExpression</CODE> based on the specified
     * parameter.
     *
     * @param cronExpression String representation of the cron expression the
     *                       new object should represent
     * @throws ParseException if the string expression cannot be parsed into a valid
     *                        <CODE>CronExpression</CODE>
     */
    public CronExpression(String cronExpression) throws ParseException {
        if (cronExpression == null) {
            throw new IllegalArgumentException("cronExpression cannot be null");
        }

        this.cronExpression = cronExpression.toUpperCase(Locale.US);

        buildExpression(this.cronExpression);
    }

    /**
     * Constructs a new {@code CronExpression} as a copy of an existing
     * instance.
     *
     * @param expression The existing cron expression to be copied
     */
    public CronExpression(CronExpression expression) {
        /*
         * We don't call the other constructor here since we need to swallow the
         * ParseException. We also elide some of the sanity checking as it is
         * not logically trippable.
         */
        this.cronExpression = expression.getCronExpression();
        try {
            buildExpression(cronExpression);
        } catch (ParseException ex) {
            throw new AssertionError();
        }
        if (expression.getTimeZone() != null) {
            setTimeZone((TimeZone) expression.getTimeZone().clone());
        }
    }

    /**
     * Indicates whether the given date satisfies the cron expression. Note that
     * milliseconds are ignored, so two Dates falling on different milliseconds
     * of the same second will always have the same result here.
     *
     * @param date the date to evaluate
     * @return a boolean indicating whether the given date satisfies the cron
     * expression
     */
    public boolean isSatisfiedBy(Date date) {
        Calendar testDateCal = Calendar.getInstance(getTimeZone());
        testDateCal.setTime(date);
        testDateCal.set(Calendar.MILLISECOND, 0);
        Date originalDate = testDateCal.getTime();

        testDateCal.add(Calendar.SECOND, -1);

        Date timeAfter = getTimeAfter(testDateCal.getTime());

        return ((timeAfter != null) && (timeAfter.equals(originalDate)));
    }

    /**
     * Returns the next date/time <I>after</I> the given date/time which
     * satisfies the cron expression.
     *
     * @param date the date/time at which to begin the search for the next valid
     *             date/time
     * @return the next valid date/time
     */
    public Date getNextValidTimeAfter(Date date) {
        return getTimeAfter(date);
    }

    /**
     * Returns the next date/time <I>after</I> the given date/time which does
     * <I>not</I> satisfy the expression
     *
     * @param date the date/time at which to begin the search for the next
     *             invalid date/time
     * @return the next valid date/time
     */
    public Date getNextInvalidTimeAfter(Date date) {
        long difference = MILLISECONDS_IN_SECOND;

        //move back to the nearest second so differences will be accurate
        Calendar adjustCal = Calendar.getInstance(getTimeZone());
        adjustCal.setTime(date);
        adjustCal.set(Calendar.MILLISECOND, 0);
        Date lastDate = adjustCal.getTime();

        Date newDate;

        //FUTURE_TODO: (QUARTZ-481) IMPROVE THIS! The following is a BAD solution to this problem. Performance will be very bad here, depending on the cron expression. It is, however A solution.

        //keep getting the next included time until it's farther than one second
        // apart. At that point, lastDate is the last valid fire time. We return
        // the second immediately following it.
        while (difference == MILLISECONDS_IN_SECOND) {
            newDate = getTimeAfter(lastDate);
            if (newDate == null) {
                break;
            }

            difference = newDate.getTime() - lastDate.getTime();

            if (difference == MILLISECONDS_IN_SECOND) {
                lastDate = newDate;
            }
        }

        return new Date(lastDate.getTime() + MILLISECONDS_IN_SECOND);
    }

    /**
     * Returns the time zone for which this <code>CronExpression</code>
     * will be resolved.
     */
    public TimeZone getTimeZone() {
        if (timeZone == null) {
            timeZone = TimeZone.getDefault();
        }

        return timeZone;
    }

    /**
     * Sets the time zone for which  this <code>CronExpression</code>
     * will be resolved.
     */
    public void setTimeZone(TimeZone timeZone) {
        this.timeZone = timeZone;
    }

    /**
     * Returns the string representation of the <CODE>CronExpression</CODE>
     *
     * @return a string representation of the <CODE>CronExpression</CODE>
     */
    @Override
    public String toString() {
        return cronExpression;
    }

    /**
     * Indicates whether the specified cron expression can be parsed into a
     * valid cron expression
     *
     * @param cronExpression the expression to evaluate
     * @return a boolean indicating whether the given expression is a valid cron
     * expression
     */
    public static boolean isValidExpression(String cronExpression) {

        try {
            new CronExpression(cronExpression);
        } catch (ParseException pe) {
            return false;
        }

        return true;
    }

    public static void validateExpression(String cronExpression) throws ParseException {

        new CronExpression(cronExpression);
    }


    ////////////////////////////////////////////////////////////////////////////
    //
    // Expression Parsing Functions
    //
    ////////////////////////////////////////////////////////////////////////////

    protected void buildExpression(String expression) throws ParseException {
        expressionParsed = true;

        try {
            initializeExpressionSets();
            parseExpressionTokens(expression);
            validateDaySpecifications();
        } catch (ParseException pe) {
            throw pe;
        } catch (Exception e) {
            throw new ParseException("Illegal cron expression format ("
                + e.toString() + ")", 0);
        }
    }

    private void initializeExpressionSets() {
        if (seconds == null) {
            seconds = new TreeSet<Integer>();
        }
        if (minutes == null) {
            minutes = new TreeSet<Integer>();
        }
        if (hours == null) {
            hours = new TreeSet<Integer>();
        }
        if (daysOfMonth == null) {
            daysOfMonth = new TreeSet<Integer>();
        }
        if (months == null) {
            months = new TreeSet<Integer>();
        }
        if (daysOfWeek == null) {
            daysOfWeek = new TreeSet<Integer>();
        }
        if (years == null) {
            years = new TreeSet<Integer>();
        }
    }

    private void parseExpressionTokens(String expression) throws ParseException {
        int exprOn = SECOND;
        StringTokenizer exprsTok = new StringTokenizer(expression, " \t", false);

        while (exprsTok.hasMoreTokens() && exprOn <= YEAR) {
            String expr = exprsTok.nextToken().trim();
            // throw an exception if L is used with other days of the month
            if (exprOn == DAY_OF_MONTH && expr.indexOf(LAST_DAY_CHAR) != -1 && expr.length() > 1 && expr.contains(COMMA_STRING)) {
                throw new ParseException("Support for specifying 'L' and 'LW' with other days of the month is not implemented", -1);
            }
            // throw an exception if L is used with other days of the week
            if (exprOn == DAY_OF_WEEK && expr.indexOf(LAST_DAY_CHAR) != -1 && expr.length() > 1 && expr.contains(COMMA_STRING)) {
                throw new ParseException("Support for specifying 'L' with other days of the week is not implemented", -1);
            }
            if (exprOn == DAY_OF_WEEK && expr.indexOf(NTH_DAY_CHAR) != -1 && expr.indexOf(NTH_DAY_CHAR, expr.indexOf(NTH_DAY_CHAR) + 1) != -1) {
                throw new ParseException("Support for specifying multiple \"nth\" days is not implemented.", -1);
            }
            StringTokenizer vTok = new StringTokenizer(expr, ",");
            while (vTok.hasMoreTokens()) {
                String v = vTok.nextToken();
                storeExpressionVals(0, v, exprOn);
            }
            exprOn++;
        }

        if (exprOn <= DAY_OF_WEEK) {
            throw new ParseException("Unexpected end of expression.", expression.length());
        }

        if (exprOn <= YEAR) {
            storeExpressionVals(0, "*", YEAR);
        }
    }

    private void validateDaySpecifications() throws ParseException {
        TreeSet<Integer> dow = getSet(DAY_OF_WEEK);
        TreeSet<Integer> dom = getSet(DAY_OF_MONTH);

        // Copying the logic from the UnsupportedOperationException below
        boolean dayOfMspec = !dom.contains(NO_SPEC);
        boolean dayOfWspec = !dow.contains(NO_SPEC);

        if (!dayOfMspec || dayOfWspec) {
            if (!dayOfWspec || dayOfMspec) {
                throw new ParseException(
                    "Support for specifying both a day-of-week AND a day-of-month parameter is not implemented.", 0);
            }
        }
    }

    protected int storeExpressionVals(int pos, String s, int type)
        throws ParseException {

        int i = skipWhiteSpace(pos, s);
        if (i >= s.length()) {
            return i;
        }
        char c = s.charAt(i);

        // Handle alphabetic values (month/day names)
        if ((c >= CHAR_A) && (c <= CHAR_Z) && (!LAST_DAY_STRING.equals(s))
               && (!LAST_WEEKDAY_STRING.equals(s)) && (!s.matches(LAST_DAY_OFFSET_PATTERN))) {
            return handleAlphabeticValue(i, s, type);
        }

        // Handle special characters and numeric values
        if (c == NO_SPECIFIC_VALUE_CHAR) {
            return handleNoSpecificValue(i, s, type);
        }

        if (c == ALL_VALUES_CHAR || c == INCREMENT_CHAR) {
            return handleAllValuesOrIncrement(i, s, type);
        }

        if (c == LAST_DAY_CHAR) {
            return handleLastDayChar(i, s, type);
        }

        if (c >= CHAR_ZERO && c <= CHAR_NINE) {
            return handleNumericValue(i, s, type);
        }

        throw new ParseException("Unexpected character: " + c, i);
    }

    private int handleAlphabeticValue(int i, String s, int type) throws ParseException {
        String sub = s.substring(i, i + SUBSTRING_LENGTH);
        int sval = -1;
        int eval = -1;
        int incr = 0;

        if (type == MONTH) {
            sval = getMonthNumber(sub) + MIN_MONTH;
            if (sval <= MIN_SECOND_OR_MINUTE) {
                throw new ParseException("Invalid Month value: '" + sub + "'", i);
            }
            if (s.length() > i + SUBSTRING_LENGTH) {
                char c = s.charAt(i + SUBSTRING_LENGTH);
                if (c == RANGE_CHAR) {
                    i += SUBSTRING_LENGTH + MIN_MONTH;
                    sub = s.substring(i, i + SUBSTRING_LENGTH);
                    eval = getMonthNumber(sub) + MIN_MONTH;
                    if (eval <= MIN_SECOND_OR_MINUTE) {
                        throw new ParseException("Invalid Month value: '" + sub + "'", i);
                    }
                }
            }
        } else if (type == DAY_OF_WEEK) {
            sval = getDayOfWeekNumber(sub);
            if (sval < MIN_SECOND_OR_MINUTE) {
                throw new ParseException("Invalid Day-of-Week value: '" + sub + "'", i);
            }
            if (s.length() > i + SUBSTRING_LENGTH) {
                char c = s.charAt(i + SUBSTRING_LENGTH);
                if (c == RANGE_CHAR) {
                    i += SUBSTRING_LENGTH + MIN_MONTH;
                    sub = s.substring(i, i + SUBSTRING_LENGTH);
                    eval = getDayOfWeekNumber(sub);
                    if (eval < MIN_SECOND_OR_MINUTE) {
                        throw new ParseException("Invalid Day-of-Week value: '" + sub + "'", i);
                    }
                } else if (c == NTH_DAY_CHAR) {
                    try {
                        i += SUBSTRING_LENGTH + MIN_MONTH;
                        nthdayOfWeek = Integer.parseInt(s.substring(i));
                        if (nthdayOfWeek < MIN_NTH_DAY_OF_WEEK || nthdayOfWeek > MAX_NTH_DAY_OF_WEEK) {
                            throw new Exception();
                        }
                    } catch (Exception e) {
                        throw new ParseException(
                            "A numeric value between 1 and 5 must follow the '#' option", i);
                    }
                } else if (c == LAST_DAY_CHAR) {
                    lastdayOfWeek = true;
                    i++;
                }
            }
        } else {
            throw new ParseException("Illegal characters for this position: '" + sub + "'", i);
        }

        if (eval != -1) {
            incr = MIN_MONTH;
        }
        addToSet(sval, eval, incr, type);
        return (i + SUBSTRING_LENGTH);
    }

    private int handleNoSpecificValue(int i, String s, int type) throws ParseException {
        i++;
        if ((i + 1) < s.length() && s.charAt(i) != SPACE_CHAR && s.charAt(i + 1) != TAB_CHAR) {
            throw new ParseException("Illegal character after '?': " + s.charAt(i), i);
        }

        if (type != DAY_OF_WEEK && type != DAY_OF_MONTH) {
            throw new ParseException("'?' can only be specified for Day-of-Month or Day-of-Week.", i);
        }

        if (type == DAY_OF_WEEK && !lastdayOfMonth) {
            int val = daysOfMonth.last();
            if (val == NO_SPEC_INT) {
                throw new ParseException(
                    "'?' can only be specified for Day-of-Month -OR- Day-of-Week.", i);
            }
        }

        addToSet(NO_SPEC_INT, -1, 0, type);
        return i;
    }

    private int handleAllValuesOrIncrement(int i, String s, int type) throws ParseException {
        char c = s.charAt(i);
        int incr = 0;

        boolean parseExpection = (c == INCREMENT_CHAR &&
            ((i + 1) >= s.length() || s.charAt(i + 1) == SPACE_CHAR || s.charAt(i + 1) == TAB_CHAR));
        if (c == ALL_VALUES_CHAR && (i + 1) >= s.length()) {
            addToSet(ALL_SPEC_INT, -1, incr, type);
            return i + 1;
        } else if (parseExpection) {
            throw new ParseException("'/' must be followed by an integer.", i);
        } else if (c == ALL_VALUES_CHAR) {
            i++;
        }

        c = s.charAt(i);
        if (c == INCREMENT_CHAR) {
            i++;
            if (i >= s.length()) {
                throw new ParseException("Unexpected end of string.", i);
            }
            incr = getNumericValue(s, i);
            i++;
            if (incr > SINGLE_DIGIT_THRESHOLD) {
                i++;
            }
            checkIncrementRange(incr, type, i);
        } else {
            incr = 1;
        }

        addToSet(ALL_SPEC_INT, -1, incr, type);
        return i;
    }

    private int handleLastDayChar(int i, String s, int type) throws ParseException {
        i++;
        if (type == DAY_OF_MONTH) {
            lastdayOfMonth = true;
        }
        if (type == DAY_OF_WEEK) {
            addToSet(MAX_DAY_OF_WEEK, MAX_DAY_OF_WEEK, 0, type);
        }
        if (type == DAY_OF_MONTH && s.length() > i) {
            char c = s.charAt(i);
            if (c == RANGE_CHAR) {
                ValueSet vs = getValue(0, s, i + 1);
                lastdayOffset = vs.value;
                if (lastdayOffset > MAX_LAST_DAY_OFFSET) {
                    throw new ParseException("Offset from last day must be <= 30", i + 1);
                }
                i = vs.pos;
            }
            if (s.length() > i) {
                c = s.charAt(i);
                if (c == WEEKDAY_CHAR) {
                    nearestWeekday = true;
                    i++;
                }
            }
        }
        return i;
    }

    private int handleNumericValue(int i, String s, int type) throws ParseException {
        char c = s.charAt(i);
        int val = Integer.parseInt(String.valueOf(c));
        i++;
        if (i >= s.length()) {
            addToSet(val, -1, -1, type);
        } else {
            c = s.charAt(i);
            if (c >= CHAR_ZERO && c <= CHAR_NINE) {
                ValueSet vs = getValue(val, s, i);
                val = vs.value;
                i = vs.pos;
            }
            i = checkNext(i, s, val, type);
            return i;
        }
        return i;
    }

    private void checkIncrementRange(int incr, int type, int idxPos) throws ParseException {
        boolean isSecondOrMinute = type == SECOND || type == MINUTE;
        boolean isHour = type == HOUR;
        boolean isDayOfMonth = type == DAY_OF_MONTH;
        boolean isDayOfWeek = type == DAY_OF_WEEK;
        boolean isMonth = type == MONTH;
        if (incr > MAX_SECOND_OR_MINUTE && isSecondOrMinute) {
            throw new ParseException("Increment > 60 : " + incr, idxPos);
        } else if (incr > MAX_HOUR && isHour) {
            throw new ParseException("Increment > 24 : " + incr, idxPos);
        } else if (incr > MAX_DAY_OF_MONTH && isDayOfMonth) {
            throw new ParseException("Increment > 31 : " + incr, idxPos);
        } else if (incr > MAX_DAY_OF_WEEK && isDayOfWeek) {
            throw new ParseException("Increment > 7 : " + incr, idxPos);
        } else if (incr > MAX_MONTH && isMonth) {
            throw new ParseException("Increment > 12 : " + incr, idxPos);
        }
    }

    protected int checkNext(int pos, String s, int val, int type)
        throws ParseException {

        int end = -1;
        int i = pos;

        if (i >= s.length()) {
            addToSet(val, end, -1, type);
            return i;
        }

        char c = s.charAt(pos);

        if (c == LAST_DAY_CHAR) {
            return handleLastDayChar(i, s, val, type);
        }

        if (c == WEEKDAY_CHAR) {
            return handleWeekdayChar(i, s, val, type);
        }

        if (c == NTH_DAY_CHAR) {
            return handleNthDayChar(i, s, val, type);
        }

        if (c == RANGE_CHAR) {
            return handleRangeChar(i, s, val, type);
        }

        if (c == INCREMENT_CHAR) {
            return handleIncrementChar(i, s, val, type);
        }

        addToSet(val, end, 0, type);
        i++;
        return i;
    }

    private int handleLastDayChar(int i, String s, int val, int type) throws ParseException {
        if (type == DAY_OF_WEEK) {
            if (val < MIN_DAY_OF_WEEK || val > MAX_DAY_OF_WEEK) {
                throw new ParseException("Day-of-Week values must be between 1 and 7", -1);
            }
            lastdayOfWeek = true;
        } else {
            throw new ParseException("'L' option is not valid here. (pos=" + i + ")", i);
        }
        TreeSet<Integer> set = getSet(type);
        set.add(val);
        i++;
        return i;
    }

    private int handleWeekdayChar(int i, String s, int val, int type) throws ParseException {
        if (type == DAY_OF_MONTH) {
            nearestWeekday = true;
        } else {
            throw new ParseException("'W' option is not valid here. (pos=" + i + ")", i);
        }
        if (val > MAX_DAY_OF_MONTH) {
            throw new ParseException("The 'W' option does not make sense with values larger than 31 (max number of days in a month)", i);
        }
        TreeSet<Integer> set = getSet(type);
        set.add(val);
        i++;
        return i;
    }

    private int handleNthDayChar(int i, String s, int val, int type) throws ParseException {
        if (type != DAY_OF_WEEK) {
            throw new ParseException("'#' option is not valid here. (pos=" + i + ")", i);
        }
        i++;
        try {
            nthdayOfWeek = Integer.parseInt(s.substring(i));
            if (nthdayOfWeek < MIN_NTH_DAY_OF_WEEK || nthdayOfWeek > MAX_NTH_DAY_OF_WEEK) {
                throw new Exception();
            }
        } catch (Exception e) {
            throw new ParseException(
                "A numeric value between 1 and 5 must follow the '#' option",
                i);
        }

        TreeSet<Integer> set = getSet(type);
        set.add(val);
        i++;
        return i;
    }

    private int handleRangeChar(int i, String s, int val, int type) throws ParseException {
        i++;
        char c = s.charAt(i);
        int v = Integer.parseInt(String.valueOf(c));
        int end = v;
        i++;
        if (i >= s.length()) {
            addToSet(val, end, 1, type);
            return i;
        }
        c = s.charAt(i);
        if (c >= CHAR_ZERO && c <= CHAR_NINE) {
            ValueSet vs = getValue(v, s, i);
            end = vs.value;
            i = vs.pos;
        }
        if (i < s.length() && ((c = s.charAt(i)) == INCREMENT_CHAR)) {
            return handleRangeWithIncrement(i, s, val, type, end);
        } else {
            addToSet(val, end, 1, type);
            return i;
        }
    }

    private int handleRangeWithIncrement(int i, String s, int val, int type, int end) throws ParseException {
        i++;
        char c = s.charAt(i);
        int v2 = Integer.parseInt(String.valueOf(c));
        i++;
        if (i >= s.length()) {
            addToSet(val, end, v2, type);
            return i;
        }
        c = s.charAt(i);
        if (c >= CHAR_ZERO && c <= CHAR_NINE) {
            ValueSet vs = getValue(v2, s, i);
            int v3 = vs.value;
            addToSet(val, end, v3, type);
            i = vs.pos;
            return i;
        } else {
            addToSet(val, end, v2, type);
            return i;
        }
    }

    private int handleIncrementChar(int i, String s, int val, int type) throws ParseException {
        if ((i + 1) >= s.length() || s.charAt(i + 1) == SPACE_CHAR || s.charAt(i + 1) == TAB_CHAR) {
            throw new ParseException("'/' must be followed by an integer.", i);
        }

        i++;
        char c = s.charAt(i);
        int v2 = Integer.parseInt(String.valueOf(c));
        i++;
        if (i >= s.length()) {
            checkIncrementRange(v2, type, i);
            addToSet(val, -1, v2, type);
            return i;
        }
        c = s.charAt(i);
        if (c >= CHAR_ZERO && c <= CHAR_NINE) {
            ValueSet vs = getValue(v2, s, i);
            int v3 = vs.value;
            checkIncrementRange(v3, type, i);
            addToSet(val, -1, v3, type);
            i = vs.pos;
            return i;
        } else {
            throw new ParseException("Unexpected character '" + c + "' after '/'", i);
        }
    }

    public String getCronExpression() {
        return cronExpression;
    }

    public String getExpressionSummary() {
        StringBuilder buf = new StringBuilder();

        buf.append("seconds: ");
        buf.append(getExpressionSetSummary(seconds));
        buf.append("\n");
        buf.append("minutes: ");
        buf.append(getExpressionSetSummary(minutes));
        buf.append("\n");
        buf.append("hours: ");
        buf.append(getExpressionSetSummary(hours));
        buf.append("\n");
        buf.append("daysOfMonth: ");
        buf.append(getExpressionSetSummary(daysOfMonth));
        buf.append("\n");
        buf.append("months: ");
        buf.append(getExpressionSetSummary(months));
        buf.append("\n");
        buf.append("daysOfWeek: ");
        buf.append(getExpressionSetSummary(daysOfWeek));
        buf.append("\n");
        buf.append("lastdayOfWeek: ");
        buf.append(lastdayOfWeek);
        buf.append("\n");
        buf.append("nearestWeekday: ");
        buf.append(nearestWeekday);
        buf.append("\n");
        buf.append("NthDayOfWeek: ");
        buf.append(nthdayOfWeek);
        buf.append("\n");
        buf.append("lastdayOfMonth: ");
        buf.append(lastdayOfMonth);
        buf.append("\n");
        buf.append("years: ");
        buf.append(getExpressionSetSummary(years));
        buf.append("\n");

        return buf.toString();
    }

    protected String getExpressionSetSummary(java.util.Set<Integer> set) {

        if (set.contains(NO_SPEC)) {
            return "?";
        }
        if (set.contains(ALL_SPEC)) {
            return "*";
        }

        StringBuilder buf = new StringBuilder();

        Iterator<Integer> itr = set.iterator();
        boolean first = true;
        while (itr.hasNext()) {
            Integer iVal = itr.next();
            String val = iVal.toString();
            if (!first) {
                buf.append(",");
            }
            buf.append(val);
            first = false;
        }

        return buf.toString();
    }

    protected String getExpressionSetSummary(java.util.ArrayList<Integer> list) {

        if (list.contains(NO_SPEC)) {
            return "?";
        }
        if (list.contains(ALL_SPEC)) {
            return "*";
        }

        StringBuilder buf = new StringBuilder();

        Iterator<Integer> itr = list.iterator();
        boolean first = true;
        while (itr.hasNext()) {
            Integer iVal = itr.next();
            String val = iVal.toString();
            if (!first) {
                buf.append(",");
            }
            buf.append(val);
            first = false;
        }

        return buf.toString();
    }

    protected int skipWhiteSpace(int i, String s) {
        for (; i < s.length() && (s.charAt(i) == SPACE_CHAR || s.charAt(i) == TAB_CHAR); i++) {
        }

        return i;
    }

    protected int findNextWhiteSpace(int i, String s) {
        for (; i < s.length() && (s.charAt(i) != SPACE_CHAR || s.charAt(i) != TAB_CHAR); i++) {
        }

        return i;
    }

    protected void addToSet(int val, int end, int incr, int type)
        throws ParseException {

        TreeSet<Integer> set = getSet(type);

        // Validate value ranges
        validateValueRanges(val, end, type);

        // Handle simple values (no increment/range)
        boolean checkSimpleValue = ((incr == 0 || incr == -1) && val != ALL_SPEC_INT);
        if (checkSimpleValue) {
            if (val != -1) {
                set.add(val);
            } else {
                set.add(NO_SPEC);
            }
            return;
        }

        // Calculate and adjust range, then add values
        RangeInfo info = calculateRangeInfo(val, end, incr, type);
        addRangeValues(set, info, type);
    }

    private void validateValueRanges(int val, int end, int type) throws ParseException {
        String typeName = "";
        int min = 0, max = 0;

        switch (type) {
            case SECOND:
            case MINUTE:
                min = MIN_SECOND_OR_MINUTE; max = MAX_SECOND_OR_MINUTE;
                typeName = (type == SECOND) ? "Second" : "Minute";
                break;
            case HOUR:
                min = MIN_HOUR; max = MAX_HOUR; typeName = "Hour";
                break;
            case DAY_OF_MONTH:
                min = MIN_DAY_OF_MONTH; max = MAX_DAY_OF_MONTH; typeName = "Day of month";
                break;
            case MONTH:
                min = MIN_MONTH; max = MAX_MONTH; typeName = "Month";
                break;
            case DAY_OF_WEEK:
                min = MIN_DAY_OF_WEEK; max = MAX_DAY_OF_WEEK; typeName = "Day-of-Week";
                break;
            case YEAR:
                min = YEAR_1970; max = MAX_YEAR; typeName = "Year";
                break;
            default:
                return; // No validation needed
        }

        // Check val and end ranges, allowing special values
        boolean isInvalidVal = (val >= 0 && val != ALL_SPEC_INT && val != NO_SPEC_INT && (val < min || val > max));
        if (isInvalidVal) {
            throw new ParseException("Value " + val + " not valid for " + typeName + " field", -1);
        }
        boolean isInvalidEnd = (end >= 0 && end != ALL_SPEC_INT && end != NO_SPEC_INT && (end < min || end > max));
        if (isInvalidEnd) {
            throw new ParseException("Value " + end + " not valid for " + typeName + " field", -1);
        }
    }

    private RangeInfo calculateRangeInfo(int val, int end, int incr, int type) {
        RangeInfo info = new RangeInfo();
        info.startAt = val;
        info.stopAt = end;
        info.increment = (val == ALL_SPEC_INT && incr <= 0) ? 1 : incr;

        // Adjust range boundaries based on type
        switch (type) {
            case SECOND:
            case MINUTE:
                if (info.stopAt == -1) {
                    info.stopAt = MAX_SECOND_OR_MINUTE;
                }
                if (info.startAt == -1 || info.startAt == ALL_SPEC_INT) {
                    info.startAt = MIN_SECOND_OR_MINUTE;
                }
                break;
            case HOUR:
                if (info.stopAt == -1) {
                    info.stopAt = MAX_HOUR;
                }
                if (info.startAt == -1 || info.startAt == ALL_SPEC_INT) {
                    info.startAt = MIN_HOUR;
                }
                break;
            case DAY_OF_MONTH:
                if (info.stopAt == -1) {
                    info.stopAt = MAX_DAY_OF_MONTH;
                }
                if (info.startAt == -1 || info.startAt == ALL_SPEC_INT) {
                    info.startAt = MIN_DAY_OF_MONTH;
                }
                break;
            case MONTH:
                if (info.stopAt == -1) {
                    info.stopAt = MAX_MONTH;
                }
                if (info.startAt == -1 || info.startAt == ALL_SPEC_INT) {
                    info.startAt = MIN_MONTH;
                }
                break;
            case DAY_OF_WEEK:
                if (info.stopAt == -1) {
                    info.stopAt = MAX_DAY_OF_WEEK;
                }
                if (info.startAt == -1 || info.startAt == ALL_SPEC_INT) {
                    info.startAt = MIN_DAY_OF_WEEK;
                }
                break;
            case YEAR:
                if (info.stopAt == -1) {
                    info.stopAt = MAX_YEAR;
                }
                if (info.startAt == -1 || info.startAt == ALL_SPEC_INT) {
                    info.startAt = YEAR_1970;
                }
                break;
            default:
                break;
        }

        // Handle range overflow
        if (info.stopAt < info.startAt) {
            switch (type) {
                case SECOND: info.max = SECONDS_MAX; break;
                case MINUTE: info.max = MINUTES_MAX; break;
                case HOUR: info.max = HOURS_MAX; break;
                case MONTH: info.max = MONTHS_MAX; break;
                case DAY_OF_WEEK: info.max = DAYS_OF_WEEK_MAX; break;
                case DAY_OF_MONTH: info.max = DAYS_OF_MONTH_MAX; break;
                case YEAR: throw new IllegalArgumentException("Start year must be less than stop year");
                default: throw new IllegalArgumentException("Unexpected type encountered");
            }
            info.stopAt += info.max;
        }

        return info;
    }

    private void addRangeValues(TreeSet<Integer> set, RangeInfo info, int type) {
        if (info.startAt == ALL_SPEC_INT && info.increment <= 0) {
            set.add(ALL_SPEC);
            return;
        }

        for (int i = info.startAt; i <= info.stopAt; i += info.increment) {
            if (info.max == -1) {
                set.add(i);
            } else {
                int i2 = i % info.max;
                boolean isMonthDayOrWeek = (i2 == 0 && (type == MONTH || type == DAY_OF_WEEK || type == DAY_OF_MONTH));
                if (isMonthDayOrWeek) {
                    i2 = info.max;
                }
                set.add(i2);
            }
        }
    }

    private static class RangeInfo {
        int startAt;
        int stopAt;
        int increment;
        int max = -1;
    }

    TreeSet<Integer> getSet(int type) {
        switch (type) {
            case SECOND:
                return seconds;
            case MINUTE:
                return minutes;
            case HOUR:
                return hours;
            case DAY_OF_MONTH:
                return daysOfMonth;
            case MONTH:
                return months;
            case DAY_OF_WEEK:
                return daysOfWeek;
            case YEAR:
                return years;
            default:
                return null;
        }
    }

    protected ValueSet getValue(int v, String s, int i) {
        char c = s.charAt(i);
        StringBuilder s1 = new StringBuilder(String.valueOf(v));
        while (c >= CHAR_ZERO && c <= CHAR_NINE) {
            s1.append(c);
            i++;
            if (i >= s.length()) {
                break;
            }
            c = s.charAt(i);
        }
        ValueSet val = new ValueSet();

        val.pos = (i < s.length()) ? i : i + 1;
        val.value = Integer.parseInt(s1.toString());
        return val;
    }

    protected int getNumericValue(String s, int i) {
        int endOfVal = findNextWhiteSpace(i, s);
        String val = s.substring(i, endOfVal);
        return Integer.parseInt(val);
    }

    protected Integer getMonthNumber(String s) {
        Integer integer = MONTH_MAP.get(s);

        if (integer == null) {
            return -1;
        }

        return integer;
    }

    protected Integer getDayOfWeekNumber(String s) {
        Integer integer = DAY_MAP.get(s);

        if (integer == null) {
            return -1;
        }

        return integer;
    }

    ////////////////////////////////////////////////////////////////////////////
    //
    // Computation Functions
    //
    ////////////////////////////////////////////////////////////////////////////

    public Date getTimeAfter(Date afterTime) {
        Calendar cl = prepareCalendar(afterTime);

        boolean gotOne = false;
        while (!gotOne) {
            if (cl.get(Calendar.YEAR) > YEAR_2999) {
                return null;
            }

            TimeComponents timeResult = calculateNextTimeComponents(cl);
            if (timeResult.shouldContinue) {
                setTimeAndContinue(cl, timeResult);
                continue;
            }

            DayResult dayResult = calculateNextDay(cl);
            if (dayResult.shouldContinue) {
                setDayAndContinue(cl, dayResult);
                continue;
            }

            MonthResult monthResult = calculateNextMonth(cl);
            if (monthResult.shouldContinue) {
                setMonthAndContinue(cl, monthResult);
                continue;
            }

            YearResult yearResult = calculateNextYear(cl);
            if (yearResult.shouldReturn) {
                return null;
            }
            if (yearResult.shouldContinue) {
                setYearAndContinue(cl, yearResult);
                continue;
            }

            cl.set(Calendar.YEAR, yearResult.year);
            gotOne = true;
        }

        return cl.getTime();
    }

    private Calendar prepareCalendar(Date afterTime) {
        Calendar cl = new java.util.GregorianCalendar(getTimeZone());
        afterTime = new Date(afterTime.getTime() + MILLISECONDS_IN_SECOND);
        cl.setTime(afterTime);
        cl.set(Calendar.MILLISECOND, 0);
        return cl;
    }

    private TimeComponents calculateNextTimeComponents(Calendar cl) {
        TimeComponents result = new TimeComponents();

        calculateNextSecond(cl, result);
        calculateNextMinute(cl, result);
        if (result.shouldContinue) {
            return result;
        }

        calculateNextHour(cl, result);

        return result;
    }

    private void calculateNextSecond(Calendar cl, TimeComponents result) {
        int sec = cl.get(Calendar.SECOND);
        SortedSet<Integer> st = seconds.tailSet(sec);

        if (st != null && st.size() != 0) {
            sec = st.first();
        } else {
            sec = seconds.first();
            result.min++;
            cl.set(Calendar.MINUTE, result.min);
        }
        cl.set(Calendar.SECOND, sec);
        result.sec = sec;
    }

    private void calculateNextMinute(Calendar cl, TimeComponents result) {
        result.min = cl.get(Calendar.MINUTE);
        result.hr = cl.get(Calendar.HOUR_OF_DAY);
        int t = -1;

        SortedSet<Integer> st = minutes.tailSet(result.min);
        if (st != null && st.size() != 0) {
            t = result.min;
            result.min = st.first();
        } else {
            result.min = minutes.first();
            result.hr++;
        }

        if (result.min != t) {
            result.shouldContinue = true;
        }
        cl.set(Calendar.MINUTE, result.min);
    }

    private void calculateNextHour(Calendar cl, TimeComponents result) {
        result.hr = cl.get(Calendar.HOUR_OF_DAY);
        result.day = cl.get(Calendar.DAY_OF_MONTH);
        int t = -1;

        SortedSet<Integer> st = hours.tailSet(result.hr);
        if (st != null && st.size() != 0) {
            t = result.hr;
            result.hr = st.first();
        } else {
            result.hr = hours.first();
            result.day++;
        }

        if (result.hr != t) {
            result.shouldContinue = true;
        }
        cl.set(Calendar.HOUR_OF_DAY, result.hr);
    }

    private void setTimeAndContinue(Calendar cl, TimeComponents result) {
        cl.set(Calendar.SECOND, 0);
        cl.set(Calendar.MINUTE, result.min);
        if (result.shouldContinue) {
            cl.set(Calendar.MINUTE, 0);
            cl.set(Calendar.DAY_OF_MONTH, result.day);
        }
        setCalendarHour(cl, result.hr);
    }

    private DayResult calculateNextDay(Calendar cl) {
        int day = cl.get(Calendar.DAY_OF_MONTH);
        int mon = cl.get(Calendar.MONTH) + 1;
        int tmon = mon;

        boolean dayOfMspec = !daysOfMonth.contains(NO_SPEC);
        boolean dayOfWspec = !daysOfWeek.contains(NO_SPEC);

        if (dayOfMspec && !dayOfWspec) {
            return calculateDayOfMonth(cl, day, mon, tmon);
        } else if (dayOfWspec && !dayOfMspec) {
            return calculateDayOfWeek(cl, day, mon);
        } else {
            throw new UnsupportedOperationException(
                "Support for specifying both a day-of-week AND a day-of-month parameter is not implemented.");
        }
    }

    private DayResult calculateDayOfMonth(Calendar cl, int day, int mon, int tmon) {
        DayResult result = new DayResult();
        result.day = day;
        result.mon = mon;
        result.tmon = tmon;

        SortedSet<Integer> st = daysOfMonth.tailSet(day);

        if (lastdayOfMonth) {
            calculateLastDayOfMonth(cl, result);
        } else if (nearestWeekday) {
            calculateNearestWeekday(cl, result);
        } else if (st != null && st.size() != 0) {
            calculateNormalDayOfMonth(cl, result, st);
        } else {
            result.day = daysOfMonth.first();
            result.mon++;
        }

        if (result.day != day || result.mon != tmon) {
            result.shouldContinue = true;
        }

        cl.set(Calendar.DAY_OF_MONTH, result.day);
        return result;
    }

    private void calculateLastDayOfMonth(Calendar cl, DayResult result) {
        if (!nearestWeekday) {
            int t = result.day;
            result.day = getLastDayOfMonth(result.mon, cl.get(Calendar.YEAR));
            result.day -= lastdayOffset;
            if (t > result.day) {
                result.mon++;
                if (result.mon > MONTHS_MAX) {
                    result.mon = 1;
                    result.tmon = MAGIC_YEAR_3333;
                    cl.add(Calendar.YEAR, 1);
                }
                result.day = 1;
            }
        } else {
            calculateLastWeekday(cl, result);
        }
    }

    private void calculateLastWeekday(Calendar cl, DayResult result) {
        int t = result.day;
        result.day = getLastDayOfMonth(result.mon, cl.get(Calendar.YEAR));
        result.day -= lastdayOffset;

        Calendar tcal = createTemporaryCalendar(cl, result);
        int ldom = getLastDayOfMonth(result.mon, cl.get(Calendar.YEAR));
        int dow = tcal.get(Calendar.DAY_OF_WEEK);

        adjustDayForWeekday(result, dow, ldom);

        updateTemporaryCalendar(tcal, cl, result);
        Date nTime = tcal.getTime();
        if (nTime.before(new Date(cl.getTimeInMillis() - MILLISECONDS_IN_SECOND))) {
            result.day = 1;
            result.mon++;
        }
    }

    private void calculateNearestWeekday(Calendar cl, DayResult result) {
        int t = result.day;
        result.day = daysOfMonth.first();

        Calendar tcal = createTemporaryCalendar(cl, result);
        int ldom = getLastDayOfMonth(result.mon, cl.get(Calendar.YEAR));
        int dow = tcal.get(Calendar.DAY_OF_WEEK);

        adjustDayForWeekday(result, dow, ldom);

        updateTemporaryCalendar(tcal, cl, result);
        Date nTime = tcal.getTime();
        if (nTime.before(new Date(cl.getTimeInMillis() - MILLISECONDS_IN_SECOND))) {
            result.day = daysOfMonth.first();
            result.mon++;
        }
    }

    private void calculateNormalDayOfMonth(Calendar cl, DayResult result, SortedSet<Integer> st) {
        int t = result.day;
        result.day = st.first();
        int lastDay = getLastDayOfMonth(result.mon, cl.get(Calendar.YEAR));
        if (result.day > lastDay) {
            result.day = daysOfMonth.first();
            result.mon++;
        }
    }

    private DayResult calculateDayOfWeek(Calendar cl, int day, int mon) {
        DayResult result = new DayResult();
        result.day = day;
        result.mon = mon;

        if (lastdayOfWeek) {
            calculateLastDayOfWeek(cl, result);
        } else if (nthdayOfWeek != 0) {
            calculateNthDayOfWeek(cl, result);
        } else {
            calculateRegularDayOfWeek(cl, result);
        }

        return result;
    }

    private void calculateLastDayOfWeek(Calendar cl, DayResult result) {
        int dow = daysOfWeek.first();
        int cDow = cl.get(Calendar.DAY_OF_WEEK);
        int daysToAdd = calculateDaysToAdd(cDow, dow);

        int lDay = getLastDayOfMonth(result.mon, cl.get(Calendar.YEAR));

        if (result.day + daysToAdd > lDay) {
            resetToNextMonth(cl, result);
            result.shouldContinue = true;
            return;
        }

        while ((result.day + daysToAdd + DAYS_IN_WEEK) <= lDay) {
            daysToAdd += DAYS_IN_WEEK;
        }

        result.day += daysToAdd;

        if (daysToAdd > 0) {
            resetTimeAndSetDay(cl, result);
            result.shouldContinue = true;
        }
    }

    private void calculateNthDayOfWeek(Calendar cl, DayResult result) {
        int dow = daysOfWeek.first();
        int cDow = cl.get(Calendar.DAY_OF_WEEK);
        int daysToAdd = calculateDaysToAdd(cDow, dow);

        boolean dayShifted = daysToAdd > 0;

        result.day += daysToAdd;
        int weekOfMonth = result.day / DAYS_IN_WEEK;
        if (result.day % DAYS_IN_WEEK > 0) {
            weekOfMonth++;
        }

        daysToAdd = (nthdayOfWeek - weekOfMonth) * DAYS_IN_WEEK;
        result.day += daysToAdd;

        if (daysToAdd < 0 || result.day > getLastDayOfMonth(result.mon, cl.get(Calendar.YEAR))) {
            resetToNextMonth(cl, result);
            result.shouldContinue = true;
        } else if (daysToAdd > 0 || dayShifted) {
            resetTimeAndSetDay(cl, result);
            result.shouldContinue = true;
        }
    }

    private void calculateRegularDayOfWeek(Calendar cl, DayResult result) {
        int cDow = cl.get(Calendar.DAY_OF_WEEK);
        int dow = daysOfWeek.first();
        SortedSet<Integer> st = daysOfWeek.tailSet(cDow);
        if (st != null && st.size() > 0) {
            dow = st.first();
        }

        int daysToAdd = calculateDaysToAdd(cDow, dow);
        int lDay = getLastDayOfMonth(result.mon, cl.get(Calendar.YEAR));

        if (result.day + daysToAdd > lDay) {
            resetToNextMonth(cl, result);
            result.shouldContinue = true;
        } else if (daysToAdd > 0) {
            cl.set(Calendar.SECOND, 0);
            cl.set(Calendar.MINUTE, 0);
            cl.set(Calendar.HOUR_OF_DAY, 0);
            cl.set(Calendar.DAY_OF_MONTH, result.day + daysToAdd);
            cl.set(Calendar.MONTH, result.mon - 1);
            result.shouldContinue = true;
        }
    }

    private int calculateDaysToAdd(int currentDow, int targetDow) {
        int daysToAdd = 0;
        if (currentDow < targetDow) {
            daysToAdd = targetDow - currentDow;
        } else if (currentDow > targetDow) {
            daysToAdd = targetDow + (DAYS_IN_WEEK - currentDow);
        }
        return daysToAdd;
    }

    private Calendar createTemporaryCalendar(Calendar cl, DayResult result) {
        Calendar tcal = Calendar.getInstance(getTimeZone());
        tcal.set(Calendar.SECOND, 0);
        tcal.set(Calendar.MINUTE, 0);
        tcal.set(Calendar.HOUR_OF_DAY, 0);
        tcal.set(Calendar.DAY_OF_MONTH, result.day);
        tcal.set(Calendar.MONTH, result.mon - 1);
        tcal.set(Calendar.YEAR, cl.get(Calendar.YEAR));
        return tcal;
    }

    private void adjustDayForWeekday(DayResult result, int dow, int ldom) {
        if (dow == Calendar.SATURDAY && result.day == 1) {
            result.day += 2;
        } else if (dow == Calendar.SATURDAY) {
            result.day -= 1;
        } else if (dow == Calendar.SUNDAY && result.day == ldom) {
            result.day -= 2;
        } else if (dow == Calendar.SUNDAY) {
            result.day += 1;
        }
    }

    private void updateTemporaryCalendar(Calendar tcal, Calendar cl, DayResult result) {
        tcal.set(Calendar.SECOND, cl.get(Calendar.SECOND));
        tcal.set(Calendar.MINUTE, cl.get(Calendar.MINUTE));
        tcal.set(Calendar.HOUR_OF_DAY, cl.get(Calendar.HOUR_OF_DAY));
        tcal.set(Calendar.DAY_OF_MONTH, result.day);
        tcal.set(Calendar.MONTH, result.mon - 1);
    }

    private void resetToNextMonth(Calendar cl, DayResult result) {
        cl.set(Calendar.SECOND, 0);
        cl.set(Calendar.MINUTE, 0);
        cl.set(Calendar.HOUR_OF_DAY, 0);
        cl.set(Calendar.DAY_OF_MONTH, 1);
        cl.set(Calendar.MONTH, result.mon);
    }

    private void resetTimeAndSetDay(Calendar cl, DayResult result) {
        cl.set(Calendar.SECOND, 0);
        cl.set(Calendar.MINUTE, 0);
        cl.set(Calendar.HOUR_OF_DAY, 0);
        cl.set(Calendar.DAY_OF_MONTH, result.day);
        cl.set(Calendar.MONTH, result.mon - 1);
    }

    private void setDayAndContinue(Calendar cl, DayResult result) {
        cl.set(Calendar.SECOND, 0);
        cl.set(Calendar.MINUTE, 0);
        cl.set(Calendar.HOUR_OF_DAY, 0);
        cl.set(Calendar.DAY_OF_MONTH, result.day);
        cl.set(Calendar.MONTH, result.mon - 1);
    }

    private MonthResult calculateNextMonth(Calendar cl) {
        MonthResult result = new MonthResult();
        result.mon = cl.get(Calendar.MONTH) + 1;
        result.year = cl.get(Calendar.YEAR);
        int t = -1;

        if (result.year > MAX_YEAR) {
            result.shouldReturn = true;
            return result;
        }

        SortedSet<Integer> st = months.tailSet(result.mon);
        if (st != null && st.size() != 0) {
            t = result.mon;
            result.mon = st.first();
        } else {
            result.mon = months.first();
            result.year++;
        }

        if (result.mon != t) {
            result.shouldContinue = true;
        }

        cl.set(Calendar.MONTH, result.mon - 1);
        return result;
    }

    private void setMonthAndContinue(Calendar cl, MonthResult result) {
        cl.set(Calendar.SECOND, 0);
        cl.set(Calendar.MINUTE, 0);
        cl.set(Calendar.HOUR_OF_DAY, 0);
        cl.set(Calendar.DAY_OF_MONTH, 1);
        cl.set(Calendar.MONTH, result.mon - 1);
        cl.set(Calendar.YEAR, result.year);
    }

    private YearResult calculateNextYear(Calendar cl) {
        YearResult result = new YearResult();
        result.year = cl.get(Calendar.YEAR);
        int t = -1;

        SortedSet<Integer> st = years.tailSet(result.year);
        if (st != null && st.size() != 0) {
            t = result.year;
            result.year = st.first();
        } else {
            result.shouldReturn = true;
            return result;
        }

        if (result.year != t) {
            result.shouldContinue = true;
        }

        return result;
    }

    private void setYearAndContinue(Calendar cl, YearResult result) {
        cl.set(Calendar.SECOND, 0);
        cl.set(Calendar.MINUTE, 0);
        cl.set(Calendar.HOUR_OF_DAY, 0);
        cl.set(Calendar.DAY_OF_MONTH, 1);
        cl.set(Calendar.MONTH, 0);
        cl.set(Calendar.YEAR, result.year);
    }

    private static class TimeComponents {
        int sec;
        int min;
        int hr;
        int day;
        boolean shouldContinue = false;
    }

    private static class DayResult {
        int day;
        int mon;
        int tmon;
        boolean shouldContinue = false;
    }

    private static class MonthResult {
        int mon;
        int year;
        boolean shouldContinue = false;
        boolean shouldReturn = false;
    }

    private static class YearResult {
        int year;
        boolean shouldContinue = false;
        boolean shouldReturn = false;
    }

    /**
     * Advance the calendar to the particular hour paying particular attention
     * to daylight saving problems.
     *
     * @param cal  the calendar to operate on
     * @param hour the hour to set
     */
    protected void setCalendarHour(Calendar cal, int hour) {
        cal.set(Calendar.HOUR_OF_DAY, hour);
        if (cal.get(Calendar.HOUR_OF_DAY) != hour && hour != HOURS_IN_DAY) {
            cal.set(Calendar.HOUR_OF_DAY, hour + 1);
        }
    }

    /**
     * NOT YET IMPLEMENTED: Returns the time before the given time
     * that the <code>CronExpression</code> matches.
     */
    public Date getTimeBefore(Date endTime) {
        // FUTURE_TODO: implement QUARTZ-423
        return null;
    }

    /**
     * NOT YET IMPLEMENTED: Returns the final time that the
     * <code>CronExpression</code> will match.
     */
    public Date getFinalFireTime() {
        // FUTURE_TODO: implement QUARTZ-423
        return null;
    }

    protected boolean isLeapYear(int year) {
        return ((year % LEAP_YEAR_DIVISOR_4 == 0 && year % LEAP_YEAR_DIVISOR_100 != 0) || (year % LEAP_YEAR_DIVISOR_400 == 0));
    }

    protected int getLastDayOfMonth(int monthNum, int year) {

        switch (monthNum) {
            case MIN_MONTH:
                return MAX_DAY_OF_MONTH;
            case 2:
                return (isLeapYear(year)) ? FEBRUARY_LEAP_DAYS : FEBRUARY_NORMAL_DAYS;
            case 3:
                return MAX_DAY_OF_MONTH;
            case 4:
                return APRIL_JUNE_SEPT_NOV_DAYS;
            case 5:
                return MAX_DAY_OF_MONTH;
            case 6:
                return APRIL_JUNE_SEPT_NOV_DAYS;
            case 7:
                return MAX_DAY_OF_MONTH;
            case 8:
                return MAX_DAY_OF_MONTH;
            case 9:
                return APRIL_JUNE_SEPT_NOV_DAYS;
            case 10:
                return MAX_DAY_OF_MONTH;
            case 11:
                return APRIL_JUNE_SEPT_NOV_DAYS;
            case MAX_MONTH:
                return MAX_DAY_OF_MONTH;
            default:
                throw new IllegalArgumentException("Illegal month number: "
                    + monthNum);
        }
    }


    private void readObject(java.io.ObjectInputStream stream)
        throws java.io.IOException, ClassNotFoundException {

        stream.defaultReadObject();
        try {
            buildExpression(cronExpression);
        } catch (Exception ignore) {
        }
    }

    @Override
    @Deprecated
    public Object clone() {
        return new CronExpression(this);
    }
}

class ValueSet {
    public int value;

    public int pos;
}
