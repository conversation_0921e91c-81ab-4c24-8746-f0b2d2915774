create table tb_project_result
(
    id                     bigint unsigned auto_increment comment '主键ID'
        primary key,
    business_type_id       bigint unsigned                               not null comment '业务类型ID，关联业务类型管理表主键',
    result_code            varchar(50)                                   not null comment '成果编号',
    result_type            varchar(2)                                    null comment '成果类型：1新系统、2重要功能模块、3一般功能模块、4事项支撑、5其它',
    project_task_name      varchar(100)                                  not null comment '项目/任务名称，限制30字符',
    priority_level         varchar(2)                                    not null comment '优先级：P1/P2/P3',
    status                 varchar(2)                                    not null comment '状态：1未开始、2进行中、3已完成、4已取消',
    milestone_requirements datetime                                      null comment '完成评审时间',
    milestone_development  datetime                                      null comment '完成开发时间',
    milestone_test         datetime                                      null comment '完成测试验收时间',
    milestone_online       datetime                                      null comment '完成上线时间',
    requirements_progress  decimal(5, 2)                                 null comment '需求评审进度百分比',
    development_progress   decimal(5, 2)                                 null comment '开发进度百分比',
    test_progress          decimal(5, 2)                                 null comment '测试验收进度百分比',
    dev_teams              varchar(1000)                                 null comment '开发组，多个用逗号分隔',
    test_teams             varchar(1000)                                 null comment '测试组，多个用逗号分隔',
    product_managers       varchar(1000)                                 null comment '产品经理(需求创建人)，多个用逗号分隔',
    dev_manpower           int                                           null comment '开发投入人力（人）',
    test_manpower          int                                           null comment '测试投入人力（人）',
    dev_workload           decimal(10, 2)                                null comment '开发工作量（人日）',
    test_workload          decimal(10, 2)                                null comment '测试工作量（人日）',
    requirement_background text                                          null comment '需求背景',
    remark                 varchar(1000)                                 null comment '备注信息',
    project_managers       varchar(500)                                  not null comment '负责项目经理，多个用逗号分隔',
    completion_time        datetime                                      null comment '完成时间，仅状态为已完成时可填写',
    archive_flag           tinyint(1) unsigned default 0                 not null comment '归档标志：0未归档、1已归档',
    create_by              varchar(30)                                   not null comment '创建人',
    create_time            datetime            default CURRENT_TIMESTAMP not null comment '创建时间',
    update_by              varchar(30)                                   not null comment '更新人',
    update_time            datetime            default CURRENT_TIMESTAMP not null comment '更新时间',
    del_flag               tinyint(4) unsigned default 0                 null comment '删除标志（0代表存在 2代表删除）'
)
    comment '项目管理主表' charset = utf8mb4;

CREATE UNIQUE INDEX idx_result_code_unique ON tb_project_result (result_code);

CREATE INDEX idx_core_query ON tb_project_result (del_flag, archive_flag, status, business_type_id);

CREATE INDEX idx_time_sort ON tb_project_result (del_flag, archive_flag, create_time);

CREATE INDEX idx_result_type_query ON tb_project_result (result_type, del_flag, create_time);

-- 修改菜单目录
update sys_menu set path = 'ProjectResult'
                  ,component = 'project/projectResult/index'
                  ,perms ='system:projectResult:list'
where menu_name = '项目成果管理';

DELETE FROM sys_menu
WHERE parent_id IN (
    SELECT menu_id FROM (
                            SELECT menu_id FROM sys_menu WHERE menu_name = '项目成果管理'
                        ) AS derived_table
);

INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1957283997029519362, '归档', 1939948158283530241, 0, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:projectResult:archive', '#', 'admin', '2025-08-18 11:30:44', 'admin', '2025-08-18 11:30:44', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1957284195151663105, '编辑', 1939948158283530241, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:projectResult:edit', '#', 'admin', '2025-08-18 11:31:31', 'admin', '2025-08-18 11:31:31', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1957284300533551105, '同步', 1939948158283530241, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:projectResult:sync', '#', 'admin', '2025-08-18 11:31:56', 'admin', '2025-08-18 11:31:56', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1957284424810778626, '查看详情', 1939948158283530241, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:projectResult:query', '#', 'admin', '2025-08-18 11:32:26', 'admin', '2025-08-18 11:32:26', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1957284553634631681, '删除', 1939948158283530241, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:projectResult:remove', '#', 'admin', '2025-08-18 11:32:57', 'admin', '2025-08-18 11:32:57', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1957284792462495746, '业务类型管理', 1939948158283530241, 5, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:projectResult:businessTypeManage', '#', 'admin', '2025-08-18 11:33:54', 'admin', '2025-08-18 11:34:56', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1957999229383806978, '新增', 1939948158283530241, 6, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:projectResult:add', '#', 'admin', '2025-08-20 10:52:49', 'admin', '2025-08-20 10:52:49', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1958104956442062849, '发送邮件', 1939948158283530241, 7, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:projectResult:sendEmail', '#', 'admin', '2025-08-20 17:52:56', 'admin', '2025-08-20 17:52:56', '');


-- 字典：项目成果管理-优先级
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1955516844139921411, '项目成果管理-优先级', 'project_outcome_priority_level', '0', 'admin', '2025-08-13 14:28:42', 'admin', '2025-08-13 14:28:42', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1955517002273570818, 0, 'P1', 'P1', 'project_outcome_priority_level', NULL, 'default', 'N', '0', 'admin', '2025-08-13 14:29:20', 'admin', '2025-08-13 14:29:20', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1955517051598585858, 1, 'P2', 'P2', 'project_outcome_priority_level', NULL, 'default', 'N', '0', 'admin', '2025-08-13 14:29:32', 'admin', '2025-08-13 14:29:32', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1955517089519288322, 2, 'P3', 'P3', 'project_outcome_priority_level', NULL, 'default', 'N', '0', 'admin', '2025-08-13 14:29:41', 'admin', '2025-08-13 14:29:41', NULL);

-- 字典：项目成果管理-状态
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1955517894083264514, '项目成果管理-状态', 'project_outcome_status', '0', 'admin', '2025-08-13 14:32:52', 'admin', '2025-08-13 14:32:52', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1955517956007968769, 1, '未开始', '1', 'project_outcome_status', NULL, 'default', 'N', '0', 'admin', '2025-08-13 14:33:07', 'admin', '2025-08-13 14:33:28', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1955518022470909953, 2, '进行中', '2', 'project_outcome_status', NULL, 'default', 'N', '0', 'admin', '2025-08-13 14:33:23', 'admin', '2025-08-13 14:33:23', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1955518099029540866, 3, '已完成', '3', 'project_outcome_status', NULL, 'default', 'N', '0', 'admin', '2025-08-13 14:33:41', 'admin', '2025-08-13 14:33:41', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1955518140494430209, 4, '已取消', '4', 'project_outcome_status', NULL, 'default', 'N', '0', 'admin', '2025-08-13 14:33:51', 'admin', '2025-08-13 14:33:51', NULL);

-- 新增项目管理组
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1955824073636327426, 15, '项目管理组', '112', 'project_outcome_dev_dept', NULL, 'default', 'N', '0', 'admin', '2025-08-14 10:49:31', 'admin', '2025-08-14 10:49:31', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (1955824209645023234, 6, '项目管理组', '112', 'project_outcome_test_dept', NULL, 'default', 'N', '0', 'admin', '2025-08-14 10:50:04', 'admin', '2025-08-14 10:50:04', NULL);


create table tb_business_type
(
    id                      bigint                                                not null
        primary key,
    business_type_name      varchar(128)                                          null comment '业务类型名称',
    business_category_major varchar(2) charset utf8mb4  default ''                null comment '所属业务大类：1国内、2海外',
    business_category_minor varchar(2) charset utf8mb4  default ''                null comment '所属业务小类：1风控、2营销、3资金、4资产、5贷后、6自营业务、7综合、8其它',
    sort                    int                                                   null comment '排序',
    business_manager        varchar(30)                                           null comment '业务负责人',
    created_by              varchar(30) charset utf8mb4 default ''                null comment '创建人',
    created_time            datetime                    default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_by              varchar(30) charset utf8mb4 default ''                null comment '更新人',
    updated_time            datetime                    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag                tinyint(4) unsigned         default 0                 null comment '删除标志（0代表存在 2代表删除）'
)
    comment '业务类型表';


CREATE TABLE `tb_story_result` (
    `id` bigint(20) NOT NULL COMMENT '需求id',
    `result_id` bigint(20) DEFAULT NULL COMMENT '成果id',
    `result_code` varchar(50) DEFAULT NULL COMMENT '成果编码',
    `product_id` bigint(20) DEFAULT NULL COMMENT '产品id',
    `product_name` varchar(255) DEFAULT NULL COMMENT '产品名称',
    `title` varchar(255) DEFAULT NULL COMMENT '需求标题',
    `status` varchar(20) DEFAULT NULL COMMENT '当前状态',
    `stage` varchar(20) DEFAULT NULL COMMENT '所处阶段',
    `opened_by` varchar(30) DEFAULT NULL COMMENT '创建人',
    `opened_date` datetime DEFAULT NULL COMMENT '创建时间',
    `assigned_to` varchar(30) DEFAULT NULL COMMENT '跟进人',
    `assigned_date` datetime DEFAULT NULL COMMENT '跟进时间',
    `reviewed_by` varchar(255) DEFAULT NULL COMMENT '评审人',
    `reviewed_date` datetime DEFAULT NULL COMMENT '评审时间',
    `closed_by` varchar(30) DEFAULT NULL COMMENT '关闭人',
    `closed_date` datetime DEFAULT NULL COMMENT '关闭时间',
    `closed_reason` varchar(255) DEFAULT NULL COMMENT '关闭原因',
    `bug_count` int(11) DEFAULT NULL COMMENT 'bug数量',
    `case_count` int(11) DEFAULT NULL COMMENT '用例数量',
    `task_count` int(11) DEFAULT NULL COMMENT '任务数量',
    `release_count` int(11) DEFAULT NULL COMMENT '发布数',
    `create_by` varchar(30) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '创建人',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_by` varchar(30) CHARACTER SET utf8mb4 DEFAULT '' COMMENT '更新人',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `del_flag` tinyint(4) unsigned DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='需求成果表';

-- 新增业务管理菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (1955562519358193665, '业务类型管理', 1939947495226982402, 2, 'businessType', 'project/businessType/index', NULL, 1, 0, 'C', '1', '0', NULL, '#', 'admin', '2025-08-13 17:30:12', 'admin', '2025-08-15 17:24:55', '');
-- 需求查询按钮权限
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (1956286338452525057, '生成成果', 1600333306947411969, 0, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:projectResult:generate', '#', 'admin', '2025-08-15 17:26:24', 'admin', '2025-08-15 17:26:24', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (1956286472829636610, '加入成果', 1600333306947411969, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:projectResult:join', '#', 'admin', '2025-08-15 17:26:56', 'admin', '2025-08-15 17:26:56', '');
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (1956286635790929921, '取消成果关联', 1600333306947411969, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:projectResult:cannel', '#', 'admin', '2025-08-15 17:27:35', 'admin', '2025-08-15 17:27:35', '');
-- 新增项目成果归档菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (1956291052388847618, '归档项目管理', 1939947495226982402, 3, 'archivingProjectResult', 'project/archivingProjectResult/index', NULL, 1, 0, 'C', '0', '0', 'system:projectResult:list', 'nested', 'admin', '2025-08-15 17:45:08', 'admin', '2025-08-15 17:45:08', '');

-- 项目成果发送人邮件字典
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (1956654634397859842, '项目成果邮件发送人', 'project_outcome_email_to', '0', 'admin', '2025-08-16 17:49:52', 'admin', '2025-08-16 17:49:52', NULL);
-- 项目成果抄送人邮件字典
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (1956654796939722754, '项目成果邮件抄送人', 'project_outcome_email_cc', '0', 'admin', '2025-08-16 17:50:37', 'admin', '2025-08-16 17:50:37', NULL);




