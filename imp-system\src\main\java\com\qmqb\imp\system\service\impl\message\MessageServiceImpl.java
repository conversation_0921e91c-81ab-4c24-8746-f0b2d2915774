package com.qmqb.imp.system.service.impl.message;

import cn.hutool.crypto.SignUtil;
import cn.hutool.crypto.digest.DigestAlgorithm;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.hzed.structure.common.util.IdUtil;
import com.hzed.structure.tool.util.JacksonUtil;
import com.qmqb.imp.common.config.MessageProperties;
import com.qmqb.imp.common.enums.MessageChannelTypeEnum;
import com.qmqb.imp.common.utils.ValidatorUtils;
import com.qmqb.imp.common.utils.spring.SpringUtils;
import com.qmqb.imp.system.domain.bo.message.BaseMsgBo;
import com.qmqb.imp.system.domain.bo.message.DdWorkMsgBo;
import com.qmqb.imp.system.domain.bo.message.SendBaseRequest;
import com.qmqb.imp.system.domain.bo.message.response.MessageResponse;
import com.qmqb.imp.system.service.ISysConfigService;
import com.qmqb.imp.system.service.message.IMessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023-01-29
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class MessageServiceImpl implements IMessageService {

    private final MessageProperties messageProperties;
    private static ISysConfigService iSysConfigService;

    /**
     * 生产环境CODE
     */
    public static final String PROD = "prod";

    @Override
    public MessageResponse<Void> sendBase(BaseMsgBo baseMsgBo) {
        //校验必填参数
        ValidatorUtils.validate(baseMsgBo);

        //签名
        SendBaseRequest sendBaseRequest = SendBaseRequest.builder().appKey(messageProperties.getAppKey()).messageContent(JacksonUtil.toJson(baseMsgBo))
            .channelType(baseMsgBo.getChannelType()).timestamp(System.currentTimeMillis()).thirdTaskNo(IdUtil.getIdStr()).build();
        sendBaseRequest.setSign(SignUtil.signParams(DigestAlgorithm.MD5, JacksonUtil.toMap(JacksonUtil.toJson(sendBaseRequest)), "&", "=", true, "&app_secret=" + messageProperties.getAppSecret()));
        log.info("消息中心请求参数:{}", JacksonUtil.toJson(sendBaseRequest));
        String response = "{\"code\":200,\"msg\":\"测试环境，不开启真实发送\",\"data\":null,\"success\":true}";
        if (PROD.equals(SpringUtils.getActiveProfile())) {
            response = HttpUtil.post(messageProperties.getUrl(), JacksonUtil.toJson(sendBaseRequest));
        }
        log.info("消息中心响应参数:{}", response);
        return JSON.parseObject(response, new TypeReference<MessageResponse<Void>>() {
        });
    }

    public static void main(String[] args) {

        BaseMsgBo baseMsgBo = DdWorkMsgBo.builder().useridList("094841683639955471").msg(DdWorkMsgBo.Msg.builder().msgtype("text")
                .text(DdWorkMsgBo.Content.builder().content("测试预警").build()).build())
            .build();
        baseMsgBo.setChannelType(MessageChannelTypeEnum.DING_DING_NOTICE.getType());
        SendBaseRequest sendBaseRequest = SendBaseRequest.builder().appKey("impEOdzG2yK").messageContent(JacksonUtil.toJson(baseMsgBo))
            .channelType(baseMsgBo.getChannelType()).timestamp(System.currentTimeMillis()).thirdTaskNo(IdUtil.getIdStr()).build();
        sendBaseRequest.setSign(SignUtil.signParams(DigestAlgorithm.MD5, JacksonUtil.toMap(JacksonUtil.toJson(sendBaseRequest)), "&", "=", true, "&app_secret=43fd5ffdcdb47952d3477582d265661a7bc38c01"));
        log.info("消息中心请求参数:{}", JacksonUtil.toJson(sendBaseRequest));
        String okResponse = HttpUtil.post("http://message.qmwallet.vip/message-center-api/v1/message/sendBase", JacksonUtil.toJson(sendBaseRequest));
        log.info("消息中心响应参数:{}", okResponse);
    }
}
