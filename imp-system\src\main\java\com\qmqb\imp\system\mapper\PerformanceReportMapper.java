package com.qmqb.imp.system.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.system.domain.PerformanceReport;
import com.qmqb.imp.system.domain.vo.PerformanceAnalysisReportVo;
import com.qmqb.imp.common.core.mapper.BaseMapperPlus;
import org.apache.ibatis.annotations.Param;

/**
 * 绩效分析报告Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
public interface PerformanceReportMapper extends BaseMapperPlus<PerformanceReportMapper, PerformanceReport, PerformanceAnalysisReportVo> {


    /**
     * 查询ai绩效报告列表
     * @param page
     * @param bo
     * @return
     */
    Page<PerformanceReport> page(@Param("page") Page<PerformanceReport> page, @Param("bo") PerformanceReport bo);
}
