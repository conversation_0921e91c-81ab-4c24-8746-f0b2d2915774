package com.qmqb.imp.system.domain.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description
 * @date 2025-08-15 09:49:12
 */
@Getter
public enum ProjectTechnologyTypeEnum {
    /**
     * ui
     */
    UI("前端", "ui"),
    /**
     * ios
     */
    IOS("ios", "ios"),
    /**
     * android
     */
    ANDROID("安卓", "android"),

    /**
     * 后端
     */
    BACK("后端", "back"),
    ;

    private final String name;

    private final String type;

    ProjectTechnologyTypeEnum(String name, String type) {
        this.name = name;
        this.type = type;
    }
}
