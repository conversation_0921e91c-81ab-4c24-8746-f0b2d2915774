package com.qmqb.imp.web.controller.performance;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.system.domain.PerformanceReport;
import com.qmqb.imp.system.service.IPerformanceReportService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2025-07-25 10:21
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/performanceReport")
public class AiPerformanceReportController {

    private final IPerformanceReportService performanceReportService;



    /**
     * 查询绩效报告列表
     */
    @SaCheckPermission("system:performanceReport:list")
    @GetMapping("/list")
    public TableDataInfo<PerformanceReport> list(PerformanceReport bo, PageQuery pageQuery) {
        return performanceReportService.queryPageList(bo, pageQuery);
    }


}
