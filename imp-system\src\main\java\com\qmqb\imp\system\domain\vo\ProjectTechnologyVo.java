package com.qmqb.imp.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.qmqb.imp.common.annotation.ExcelDictFormat;
import com.qmqb.imp.common.convert.ExcelDictConvert;
import lombok.Data;

import java.io.Serializable;


/**
 * 代码库相关技术栈视图对象 tb_project_technology
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@ExcelIgnoreUnannotated
public class ProjectTechnologyVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 项目ID
     */
    @ExcelProperty(value = "项目ID")
    private Long pId;

    /**
     * 框架类型 ui/back/android/ios
     */
    @ExcelProperty(value = "框架类型 ui/back/android/ios")
    private String type;

    /**
     * 版本
     */
    @ExcelProperty(value = "版本")
    private String version;


    /**
     * framework
     */
    @ExcelProperty(value = "框架")
    private String framework;
}
