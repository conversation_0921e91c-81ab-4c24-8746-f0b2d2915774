package com.qmqb.imp.web.controller.business;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.qmqb.imp.common.annotation.Log;
import com.qmqb.imp.common.annotation.RepeatSubmit;
import com.qmqb.imp.common.core.controller.BaseController;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.qmqb.imp.common.core.domain.R;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.validate.AddGroup;
import com.qmqb.imp.common.core.validate.EditGroup;
import com.qmqb.imp.common.enums.BusinessType;
import com.qmqb.imp.system.domain.bo.ReleaseRecordBo;
import com.qmqb.imp.system.domain.vo.ReleaseRecordVo;
import com.qmqb.imp.system.service.IReleaseRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 项目成果管理
 *
 * <AUTHOR>
 * @date 2025-01-01
 */
@Validated
@Deprecated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/releaseRecord")
public class ReleaseRecordController extends BaseController {

    private final IReleaseRecordService releaseRecordService;

    /**
     * 项目成果管理记录列表
     */
    @SaCheckPermission("system:releaseRecord:list")
    @GetMapping("/list")
    public TableDataInfo<ReleaseRecordVo> list(ReleaseRecordBo bo, PageQuery pageQuery) {
        return releaseRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取项目成果管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:releaseRecord:query")
    @GetMapping("/{id}")
    public R<ReleaseRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(releaseRecordService.queryById(id));
    }

    /**
     * 新增项目成果管理记录
     */
    @SaCheckPermission("system:releaseRecord:add")
    @Log(title = "项目成果管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ReleaseRecordBo bo) {
        return toAjax(releaseRecordService.insertByBo(bo));
    }

    /**
     * 修改项目成果管理记录
     */
    @SaCheckPermission("system:releaseRecord:edit")
    @Log(title = "项目成果管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ReleaseRecordBo bo) {
        return toAjax(releaseRecordService.updateByBo(bo));
    }
}
