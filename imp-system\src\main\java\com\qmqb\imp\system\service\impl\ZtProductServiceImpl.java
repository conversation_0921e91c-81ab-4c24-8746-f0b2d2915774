package com.qmqb.imp.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.qmqb.imp.system.domain.bo.ZtProductBo;
import com.qmqb.imp.system.domain.vo.ZtProductVo;
import com.qmqb.imp.system.domain.ZtProduct;
import com.qmqb.imp.system.mapper.ZtProductMapper;
import com.qmqb.imp.system.service.IZtProductService;

import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.Set;

/**
 * 禅道产品Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-18
 */
@RequiredArgsConstructor
@Service
public class ZtProductServiceImpl implements IZtProductService {

    private final ZtProductMapper baseMapper;

    /**
     * 查询禅道产品
     */
    @Override
    public ZtProductVo queryById(Integer id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询禅道产品列表
     */
    @Override
    public TableDataInfo<ZtProductVo> queryPageList(ZtProductBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ZtProduct> lqw = buildQueryWrapper(bo);
        Page<ZtProductVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询禅道产品列表
     */
    @Override
    public List<ZtProductVo> queryList(ZtProductBo bo) {
        LambdaQueryWrapper<ZtProduct> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ZtProduct> buildQueryWrapper(ZtProductBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ZtProduct> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getProgram() != null, ZtProduct::getProgram, bo.getProgram());
        lqw.like(StringUtils.isNotBlank(bo.getName()), ZtProduct::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getCode()), ZtProduct::getCode, bo.getCode());
        lqw.eq(StringUtils.isNotBlank(bo.getBind()), ZtProduct::getBind, bo.getBind());
        lqw.eq(bo.getLine() != null, ZtProduct::getLine, bo.getLine());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), ZtProduct::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ZtProduct::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getSubStatus()), ZtProduct::getSubStatus, bo.getSubStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getDesc()), ZtProduct::getDesc, bo.getDesc());
        lqw.eq(StringUtils.isNotBlank(bo.getPO()), ZtProduct::getPO, bo.getPO());
        lqw.eq(StringUtils.isNotBlank(bo.getQD()), ZtProduct::getQD, bo.getQD());
        lqw.eq(StringUtils.isNotBlank(bo.getRD()), ZtProduct::getRD, bo.getRD());
        lqw.eq(StringUtils.isNotBlank(bo.getFeedback()), ZtProduct::getFeedback, bo.getFeedback());
        lqw.eq(StringUtils.isNotBlank(bo.getAcl()), ZtProduct::getAcl, bo.getAcl());
        lqw.eq(StringUtils.isNotBlank(bo.getWhitelist()), ZtProduct::getWhitelist, bo.getWhitelist());
        lqw.eq(StringUtils.isNotBlank(bo.getCreatedBy()), ZtProduct::getCreatedBy, bo.getCreatedBy());
        lqw.eq(bo.getCreatedDate() != null, ZtProduct::getCreatedDate, bo.getCreatedDate());
        lqw.eq(StringUtils.isNotBlank(bo.getCreatedVersion()), ZtProduct::getCreatedVersion, bo.getCreatedVersion());
        lqw.eq(bo.getOrder() != null, ZtProduct::getOrder, bo.getOrder());
        lqw.eq(StringUtils.isNotBlank(bo.getDeleted()), ZtProduct::getDeleted, bo.getDeleted());
        return lqw;
    }

    /**
     * 新增禅道产品
     */
    @Override
    public Boolean insertByBo(ZtProductBo bo) {
        ZtProduct add = BeanUtil.toBean(bo, ZtProduct.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改禅道产品
     */
    @Override
    public Boolean updateByBo(ZtProductBo bo) {
        ZtProduct update = BeanUtil.toBean(bo, ZtProduct.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ZtProduct entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除禅道产品
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Integer> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 通过Id列表获取产品列表
     * @param ids
     * @return
     */
    @Override
    public List<ZtProduct> productNameListByIds(Set<Integer> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        return this.baseMapper.selectList(Wrappers.lambdaQuery(ZtProduct.class)
            .select(ZtProduct::getId, ZtProduct::getName)
            .in(ZtProduct::getId, ids)
            .eq(ZtProduct::getDeleted, "0"));
    }
}
