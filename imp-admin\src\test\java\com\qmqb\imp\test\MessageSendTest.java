package com.qmqb.imp.test;

import cn.hutool.core.util.StrUtil;
import com.qmqb.imp.common.enums.MessageChannelTypeEnum;
import com.qmqb.imp.job.api.config.DingTalkConfig;
import com.qmqb.imp.system.domain.bo.message.DdRobotMsgBo;
import com.qmqb.imp.system.service.message.IMessageService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

/**
 * 信息通知测试类
 *
 * <AUTHOR>
 * @since 2024-09-02
 */
@Slf4j
@SpringBootTest
public class MessageSendTest {

    @Autowired
    private DingTalkConfig dingTalkConfig;
    @Autowired
    private IMessageService messageService;

    /**
     * 钉钉机器人
     */
    @Test
    public void testDingtalk() {
        String dayOvertimeTag = "【日考勤统计】昨天（{time}）加班到21点30之后组员名单：{nameList}，请相关组长同步加班原因。";
        Map<String, String> map = new HashMap(16);
        map.put("time", "2024-09-02");
        map.put("nameList", null);
        String content = StrUtil.format(dayOvertimeTag, map);
        DdRobotMsgBo robotMsgBo = DdRobotMsgBo.builder().url(dingTalkConfig.getRobotUrl()).msgtype("text").content(content).build();
        robotMsgBo.setChannelType(MessageChannelTypeEnum.DING_DING_MESSAGE.getType());
        try {
            messageService.sendBase(robotMsgBo);
        } catch (Exception e) {
            log.error("调用消息中心发送消息异常。异常信息：{}", e.getMessage());
        }
    }
}
