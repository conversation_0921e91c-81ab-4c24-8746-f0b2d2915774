package com.qmqb.imp.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;


/**
 * 代码库相关技术栈对象 tb_project_technology
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
@TableName("tb_project_technology")
public class    ProjectTechnology implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 项目ID
     */
    private Long pId;
    /**
     * 框架类型 ui/back/android/ios
     */
    private String type;
    /**
     * 版本号
     */
    private String version;

    /**
     * 框架名称
     */
    private String framework;


}
