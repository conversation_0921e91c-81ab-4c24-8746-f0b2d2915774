package com.qmqb.imp.system.domain.vo;

import com.qmqb.imp.system.domain.ProjectTechnology;
import lombok.Data;

import java.util.List;

/**
 * 代码库相关技术栈业务对象 tb_project_technology按类型分类
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@Data
public class ProjectTechnologyGroupVo {

    /**
     * ui
     */
    private List<ProjectTechnologyVo> ui;

    /**
     * 后端
     */
    private List<ProjectTechnologyVo> back;

    /**
     * android
     */
    private List<ProjectTechnologyVo> android;

    /**
     * ios
     */
    private List<ProjectTechnologyVo> ios;
}
