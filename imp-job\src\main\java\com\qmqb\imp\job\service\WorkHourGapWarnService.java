package com.qmqb.imp.job.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzed.structure.log.annotation.TraceId;
import com.qmqb.imp.common.constant.Constants;
import com.qmqb.imp.common.constant.UserConstants;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.MessageChannelTypeEnum;
import com.qmqb.imp.job.api.config.DingTalkConfig;
import com.qmqb.imp.system.domain.bo.message.DdRobotMsgBo;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import com.qmqb.imp.system.domain.vo.UserLeaveVo;
import com.qmqb.imp.system.runner.HolidayUtil;
import com.qmqb.imp.system.service.ISysDeptService;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.IUserLeaveService;
import com.qmqb.imp.system.service.IWorkStatService;
import com.qmqb.imp.system.service.message.IMessageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 自动同步请假时长数据
 *
 * <AUTHOR>
 * @since 2024/12/4 16:00
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkHourGapWarnService {

    private final IWorkStatService workStatService;
    private final IUserLeaveService userLeaveService;
    private final ISysDeptService sysDeptService;
    private final DingTalkConfig dingTalkConfig;
    private final IMessageService messageService;
    private final ISysUserService userService;

    @TraceId("小组工时预警定时任务")
    @XxlJob("workHourGapWarnServiceJobHandler")
    public ReturnT<String> workHourGapWarnServiceJobHandler(String param) {
        Integer year = LocalDateTime.now().getYear();
        Integer month = LocalDateTime.now().getMonthValue();
        try {
            if (StringUtils.isNotBlank(param)) {
                String[] dateStrS = param.split("-");
                year = Integer.parseInt(dateStrS[0]);
                month = Integer.parseInt(dateStrS[1]);
            }
        } catch (Exception e) {
            XxlJobLogger.log("入参格式有误");
            log.error("小组工时预警定时任务执行异常", e);
            return ReturnT.FAIL;
        }
        try {
            XxlJobLogger.log("开始执行小组工时预警定时任务...");
            log.info("开始执行小组工时预警定时任务，入参为：" + param);
            val sw = new StopWatch();
            sw.start();

            //以绩效综合统计为主表，再去查询代码行数和预警
            Page page = new Page(1, Integer.MAX_VALUE);

            List<Long> groupIdList = sysDeptService.listTecCenterDeptIdList();
            List<String> highGapGroups = new ArrayList<>();

            // 过滤新员工组
            groupIdList = groupIdList.stream().filter(groupId -> !UserConstants.NEW_MEM_DEPT_ID.equals(groupId)).collect(Collectors.toList());
            List<TrackWorkResultVO> perfStatList;
            perfStatList = workStatService.getPerfStatList2(page, groupIdList, year, month,
                null, null);
            if (CollectionUtil.isNotEmpty(perfStatList)) {
                // 过滤新员工
                perfStatList = filterNewEmployee(perfStatList, year, month);

                if (CollectionUtil.isNotEmpty(perfStatList)) {
                    List<UserLeaveVo> userLeaveVos = userLeaveService.statisticUserLeave(groupIdList, year.toString(), month.toString());
                    // 计算当月请假超过21小时的人员
                    Set<String> groupUserWorkTimes = HolidayUtil.getOverTimeLeaveNickName(userLeaveVos, year, month);

                    // 计算每个小组的最高工时与最低工时差距
                    Map<String, Map<String, BigDecimal>> groupUserWorkTimeMap = perfStatList.stream()
                        .filter(perf -> !groupUserWorkTimes.contains(perf.getWorkUsername()))
                        .collect(Collectors.groupingBy(
                            TrackWorkResultVO::getWorkGroup,
                            Collectors.toMap(
                                TrackWorkResultVO::getWorkUsername,
                                perf -> new BigDecimal(perf.getKqAttendanceWorkTime()),
                                // 如果有重复用户名，保留第一个
                                (existing, replacement) -> existing
                            )
                        ));
                    log.info("工时预警统计结果：{}", JSON.toJSONString(groupUserWorkTimeMap));
                    for (Map.Entry<String, Map<String, BigDecimal>> entry : groupUserWorkTimeMap.entrySet()) {
                        String group = entry.getKey();
                        // 时间单位分钟
                        Map<String, BigDecimal> userWorkTime = entry.getValue();

                        BigDecimal maxWorkTime = userWorkTime.values().stream().max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
                        BigDecimal minWorkTime = userWorkTime.values().stream().min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
                        BigDecimal gap = maxWorkTime.subtract(minWorkTime);
                        if (gap.compareTo(new BigDecimal("40").multiply(new BigDecimal("60"))) >= 0) {
                            highGapGroups.add(group + "【" + gap.divide(new BigDecimal("60"), 2, RoundingMode.DOWN) + "】");
                        }
                    }
                }
            }


            if (CollectionUtil.isNotEmpty(highGapGroups)) {
                send(Constants.WORK_HOUR_GAP_TAG, StringUtils.join(highGapGroups, "、"));
            } else {
                send(Constants.WORK_HOUR_NORMAL_TAG, null);
            }
            sw.stop();
            XxlJobLogger.log("小组工时预警定时任务任务执行结束,耗时:{}", sw.getTotalTimeMillis());
            log.info("小组工时预警定时任务任务执行结束,耗时:{}", sw.getTotalTimeMillis());

            return ReturnT.SUCCESS;
        } catch (Exception e) {
            XxlJobLogger.log(e);
            log.error("小组工时预警定时任务执行异常", e);
            return ReturnT.FAIL;
        }
    }


    /**
     * 过滤当月新员工
     */
    private List<TrackWorkResultVO> filterNewEmployee(List<TrackWorkResultVO> perfStatList, Integer year, Integer month) {
        LocalDate startDate = LocalDate.of(year, month, 1);
        LocalDate endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());
        List<SysUser> sysUsers = userService.listNewEmployee(startDate, endDate);
        if (CollectionUtil.isEmpty(sysUsers)) {
            return perfStatList;
        }
        Set<String> names = sysUsers.stream().map(SysUser::getNickName).collect(Collectors.toSet());
        return perfStatList.stream().filter(perfStat -> !names.contains(perfStat.getWorkUsername())).collect(Collectors.toList());
    }

    /**
     * 发送预警
     *
     * @param template  模板
     * @param groupList 用户列表
     */
    private void send(String template, String groupList) {
        Map<String, String> map = new HashMap(16);
        map.put("groupList", groupList);
        String content = StrUtil.format(template, map);
        DdRobotMsgBo robotMsgBo = DdRobotMsgBo.builder().url(dingTalkConfig.getRobotUrl()).msgtype("text").content(content).build();
        robotMsgBo.setChannelType(MessageChannelTypeEnum.DING_DING_MESSAGE.getType());
        try {
            messageService.sendBase(robotMsgBo);
        } catch (Exception e) {
            log.error("调用消息中心发送消息异常。异常信息：{}", e.getMessage());
        }
    }
}
