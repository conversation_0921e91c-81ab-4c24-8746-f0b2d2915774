package com.qmqb.imp.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.qmqb.imp.common.constant.CommConstants;
import com.qmqb.imp.common.core.domain.dto.BranchStatisticsDTO;
import com.qmqb.imp.common.core.domain.dto.CodeQueryDTO;
import com.qmqb.imp.common.exception.ServiceException;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.system.domain.Project;
import com.qmqb.imp.system.domain.ProjectTechnology;
import com.qmqb.imp.system.domain.ScanProject;
import com.qmqb.imp.system.domain.bo.ProjectBo;
import com.qmqb.imp.system.domain.bo.ProjectTechnologyBo;
import com.qmqb.imp.system.domain.bo.ProjectTechnologyGroupBO;
import com.qmqb.imp.system.domain.enums.ProjectTechnologyTypeEnum;
import com.qmqb.imp.system.domain.vo.ProjectTechnologyGroupVo;
import com.qmqb.imp.system.domain.vo.ProjectTechnologyVo;
import com.qmqb.imp.system.domain.vo.ProjectVo;
import com.qmqb.imp.system.mapper.BranchMapper;
import com.qmqb.imp.system.mapper.ProjectMapper;
import com.qmqb.imp.system.mapper.ProjectTechnologyMapper;
import com.qmqb.imp.system.mapper.ScanProjectMapper;
import com.qmqb.imp.system.service.IBusinessConfigService;
import com.qmqb.imp.system.service.IProjectService;
import com.qmqb.imp.system.service.ISysDeptService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 项目管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-07
 */
@RequiredArgsConstructor
@Service
public class ProjectServiceImpl implements IProjectService {


    @Autowired
    private ProjectMapper projectMapper;

    @Autowired
    private BranchMapper branchMapper;

    @Autowired
    private ISysDeptService iSysDeptService;

    @Autowired
    private IBusinessConfigService iBusinessConfigService;

    @Autowired
    private ScanProjectMapper scanProjectMapper;

    @Autowired
    private ProjectTechnologyMapper projectTechnologyMapper;

    @Override
    public Page<ProjectVo> page(ProjectBo request) {
        Page<ProjectVo> page = new Page<>(request.getPageNo(),request.getPageSize());
        if (StrUtil.isNotBlank(request.getOrderByField())) {
            request.setOrderByField(StringUtils.toUnderScoreCase(request.getOrderByField()));
        }
        List<ProjectVo> projectVos = this.projectMapper.getLists(page, request);
        if (CollectionUtil.isNotEmpty(projectVos)) {
            Set<Long> pIds = projectVos.stream().map(ProjectVo::getPId).collect(Collectors.toSet());
            List<ProjectTechnology> projectTechnologies = projectTechnologyMapper.selectList(new LambdaQueryWrapper<ProjectTechnology>().in(ProjectTechnology::getPId, pIds));
            Map<Long, List<ProjectTechnologyVo>> pidMap = projectTechnologies.stream().map(projectTechnology -> {
                ProjectTechnologyVo projectTechnologyVo = new ProjectTechnologyVo();
                BeanUtils.copyProperties(projectTechnology, projectTechnologyVo);
                return projectTechnologyVo;
            }).collect(Collectors.groupingBy(ProjectTechnologyVo::getPId));
            projectVos.forEach(projectVo -> {
                ProjectTechnologyGroupVo projectTechnologyGroupVo = new ProjectTechnologyGroupVo();
                List<ProjectTechnologyVo> projectTechnologyVos = pidMap.get(projectVo.getPId());
                if (CollectionUtil.isNotEmpty(projectTechnologyVos)) {
                    Map<String, List<ProjectTechnologyVo>> byTypeMap = projectTechnologyVos.stream().collect(Collectors.groupingBy(ProjectTechnologyVo::getType));
                    projectTechnologyGroupVo.setUi(byTypeMap.get(ProjectTechnologyTypeEnum.UI.getType()));
                    projectTechnologyGroupVo.setBack(byTypeMap.get(ProjectTechnologyTypeEnum.BACK.getType()));
                    projectTechnologyGroupVo.setAndroid(byTypeMap.get(ProjectTechnologyTypeEnum.ANDROID.getType()));
                    projectTechnologyGroupVo.setIos(byTypeMap.get(ProjectTechnologyTypeEnum.IOS.getType()));
                    projectVo.setTechnologies(projectTechnologyGroupVo);
                }
            });
        }
        page.setRecords(projectVos);
        return page;
    }

    @Override
    public ProjectVo getByProjectId(Long pId) {
       return this.projectMapper.getByProjectId(pId);
    }

    @Override
    public int insert(Project project) {
        return this.projectMapper.insert(project);
    }

    @Override
    public List<ProjectVo> getByProjects() {
        return this.projectMapper.getByProjects();
    }

    @Override
    public int batchsByProjectIds(List<Long> ids) {
        return this.projectMapper.deleteBatchIds(ids);
    }

    @Override
    public Long selectProjectsCountByCreateTime(Date beginTime, Date endTime) {
        return this.projectMapper.selectCount(Wrappers.lambdaQuery(Project.class).between(Project::getPCreatetime, beginTime, endTime));
    }

    @Override
    public Boolean updateBatchById(List<Project> pros) {
        return this.projectMapper.updateBatchById(pros);
    }

    @Override
    public Boolean insertBatch(List<Project> pros) {
        return this.projectMapper.insertBatch(pros);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(ProjectBo request) {
        if (request==null && request.getPId()==null){
            throw new ServiceException("项目ID不能为空");
        }
        ProjectVo byProjectId = projectMapper.selectVoById(request.getPId());
        if (byProjectId==null){
            throw new ServiceException("项目不存在");
        }
        UpdateWrapper<Project> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("p_id",request.getPId());
        updateWrapper.set("p_dev_dept",request.getPDevDept());
        updateWrapper.set("p_test_dept",request.getPTestDept());
        updateWrapper.set("p_broad_business",request.getPBroadBusiness());
        updateWrapper.set("p_narrow_business",request.getPNarrowBusiness());
        this.projectMapper.update(null,updateWrapper);
        //修改代码扫描记录表
        UpdateWrapper<ScanProject> updateWrapper1 = new UpdateWrapper<>();
        updateWrapper1.eq("p_id",request.getPId());
        updateWrapper1.eq("last_scan_flag", CommConstants.CommonVal.ONE);
        updateWrapper1.eq("del_flag",CommConstants.CommonVal.ZERO);
        updateWrapper1.set("dev_dept",request.getPDevDept());
        this.scanProjectMapper.update(null,updateWrapper1);

        this.projectTechnologyMapper.delete(new LambdaQueryWrapper<ProjectTechnology>().eq(ProjectTechnology::getPId, byProjectId.getPId()));
        ProjectTechnologyGroupBO technologies = request.getTechnologies();
        List<ProjectTechnology> projectTechnologies = new ArrayList<>();
        // 存在不涉及，则只会保留不涉及
        projectTechnologies.addAll(filterTechnologies(technologies.getUi()));
        projectTechnologies.addAll(filterTechnologies(technologies.getAndroid()));
        projectTechnologies.addAll(filterTechnologies(technologies.getIos()));
        projectTechnologies.addAll(filterTechnologies(technologies.getBack()));
        projectTechnologies.stream().filter(projectTechnology -> StringUtils.isNotBlank(projectTechnology.getFramework())).forEach(projectTechnology -> projectTechnology.setPId(request.getPId()));
        this.projectTechnologyMapper.insertBatch(projectTechnologies);

        return true;
    }

    @Override
    public List<Project> getByProjects(CodeQueryDTO request) {
        boolean flag=((request.getPDevDept()!=null && !request.getPDevDept().isEmpty()) || (request.getPTestDept()!=null && !request.getPTestDept().isEmpty()) || request.getPBroadBusiness()!=null || request.getPNarrowBusiness()!=null);
        if (flag){
            QueryWrapper<Project> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq(request.getPDevDept()!=null && !request.getPDevDept().isEmpty(),"p_dev_dept",request.getPDevDept());
            queryWrapper.eq(request.getPTestDept()!=null && !request.getPTestDept().isEmpty(),"p_test_dept",request.getPTestDept());
            queryWrapper.eq(request.getPBroadBusiness()!=null,"p_broad_business",request.getPBroadBusiness());
            queryWrapper.eq(request.getPNarrowBusiness()!=null,"p_narrow_business",request.getPNarrowBusiness());
            return projectMapper.selectList(queryWrapper);
        }
        return new ArrayList<>();
    }

    @Override
    public List<BranchStatisticsDTO> branchStatisticsList(Integer number) {
        return projectMapper.branchStatisticsList(number);
    }

    @Override
    public List<Project> selectProjectByIds(List<Long> pids) {
        return projectMapper.selectBatchIds(pids);
    }

    private List<ProjectTechnology> filterTechnologies(List<ProjectTechnology> projectTechnologies) {
        // 如果存在none，则只保留none
        String framework = "none";
        Optional<ProjectTechnology> first = projectTechnologies.stream().filter(projectTechnology -> StringUtils.isNotBlank(projectTechnology.getFramework()) && projectTechnology.getFramework().equals(framework)).findFirst();
        return first.map(Arrays::asList).orElse(projectTechnologies);
    }
}
