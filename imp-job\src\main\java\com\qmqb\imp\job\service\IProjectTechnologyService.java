package com.qmqb.imp.job.service;

import com.qmqb.imp.system.domain.ProjectTechnology;
import com.qmqb.imp.system.domain.vo.ProjectTechnologyVo;
import com.qmqb.imp.system.domain.bo.ProjectTechnologyBo;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 代码库相关技术栈Service接口
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface IProjectTechnologyService {

    /**
     * 查询代码库相关技术栈
     * @param id
     * @return
     */
    ProjectTechnologyVo queryById(Long id);

    /**
     * 查询代码库相关技术栈列表
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<ProjectTechnologyVo> queryPageList(ProjectTechnologyBo bo, PageQuery pageQuery);

    /**
     * 查询代码库相关技术栈列表
     * @param bo
     * @return
     */
    List<ProjectTechnologyVo> queryList(ProjectTechnologyBo bo);

    /**
     * 新增代码库相关技术栈
     * @param bo
     * @return
     */
    Boolean insertByBo(ProjectTechnologyBo bo);

    /**
     * 修改代码库相关技术栈
     * @param bo
     * @return
     */
    Boolean updateByBo(ProjectTechnologyBo bo);

    /**
     * 校验并批量删除代码库相关技术栈信息
     * @param ids
     * @param isValid
     * @return
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
