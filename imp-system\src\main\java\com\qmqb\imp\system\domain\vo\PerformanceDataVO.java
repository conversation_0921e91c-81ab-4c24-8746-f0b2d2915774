package com.qmqb.imp.system.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-06-23 14:41
 */
@Data
public class PerformanceDataVO {

    @Schema(description ="个人绩效数据")
    private List<TrackWorkResultVO>  personTrackWorkResult;

    @Schema(description ="组绩效数据")
    private List<TrackWorkResult>  teamTrackWorkResult;

    @Schema(description ="岗位绩效数据")
    private List<TrackWorkResult> postTrackWorkResult;

    @Data
    public static class TrackWorkResult {

        @Schema(description = "角色名称")
        private String roleName;

        @Schema(description = "组别")
        private String workGroup;

        @Schema(description = "工作时长(时)")
        private BigDecimal kqAttendanceWorkTime;

        @Schema(description = "解决bug数")
        private BigDecimal workResolveBugCount;

        @Schema(description = "调整代码(小于1000行的新增)")
        private BigDecimal pushCodeLines;

        @Schema(description = "完成禅道任务")
        private BigDecimal allWorkTaskCount;

        @Schema(description = "文档")
        private BigDecimal docCount;

        @Schema(description = "预警")
        private BigDecimal warnCount;

        @Schema(description = "耗时超过10天的任务数量")
        private BigDecimal workOverTenDaysTaskCount;

        @Schema(description = "创建bug数")
        private BigDecimal createBugCount;

        @Schema(description = "关闭bug数")
        private BigDecimal workCloseBugCount;

        @Schema(description = "完成执行用例数量")
        private BigDecimal workCaseCount;

        @Schema(description = "禅道耗时占比")
        private String workTaskTimePercent;

        @Schema(description = "平均饱和度：人均工作时长/标准⼯作时⻓*100%")
        private String saturation;

        @Schema(description = " 任务效率： 人均工作时长 / 人均完成禅道任务")
        private BigDecimal taskEfficiency;

        @Schema(description = "代码效率：人均调整代码行数/ 人均工作时长")
        private BigDecimal codeEfficiency;

        @Schema(description = "禅道任务耗时")
        private BigDecimal workConsumedTime;

    }

}
