<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.PerformanceReportMapper">

    <resultMap type="com.qmqb.imp.system.domain.PerformanceReport" id="PerformanceReportResult">
        <result property="id" column="id"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="reportType" column="report_type"/>
        <result property="userName" column="user_name"/>
        <result property="groupName" column="group_name"/>
        <result property="postName" column="post_name"/>
        <result property="reportUrl" column="report_url"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="page" resultType="com.qmqb.imp.system.domain.PerformanceReport">
        select * from tb_performance_report
        where 1=1
        <if test="bo.year != null and bo.year != ''">
         and year = #{bo.year}
        </if>
        <if test="bo.month != null and bo.month != ''">
         and month = #{bo.month}
        </if>
        <if test="bo.reportType != null and bo.reportType != ''">
         and report_type = #{bo.reportType}
        </if>
        <if test="bo.userName != null and bo.userName != ''">
         and user_name like concat('%',#{bo.userName},'%')
        </if>
        <if test="bo.groupName != null and bo.groupName != ''">
         and group_name = #{bo.groupName}
        </if>
        <if test="bo.postName != null and bo.postName != ''">
         and post_name like concat('%',#{bo.postName},'%')
        </if>
    </select>


</mapper>
