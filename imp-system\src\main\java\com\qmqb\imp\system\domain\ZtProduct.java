package com.qmqb.imp.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.qmqb.imp.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 禅道产品对象 zt_product
 *
 * <AUTHOR>
 * @date 2024-11-18
 */
@Data
@TableName("zt_product")
public class ZtProduct {

    private static final long serialVersionUID=1L;

    /**
     *
     */
    @TableId(value = "id")
    private Integer id;
    /**
     *
     */
    private Integer program;
    /**
     *
     */
    private String name;
    /**
     *
     */
    private String code;
    /**
     *
     */
    private String bind;
    /**
     *
     */
    private Integer line;
    /**
     *
     */
    private String type;
    /**
     *
     */
    private String status;
    /**
     *
     */
    @TableField("subStatus")
    private String subStatus;
    /**
     *
     */
    @TableField("`desc`")
    private String desc;
    /**
     *
     */
    @TableField("PO")
    private String PO;
    /**
     *
     */
    @TableField("QD")
    private String QD;
    /**
     *
     */
    @TableField("RD")
    private String RD;
    /**
     *
     */
    private String feedback;
    /**
     *
     */
    private String acl;
    /**
     *
     */
    private String whitelist;
    /**
     *
     */
    @TableField("createdBy")
    private String createdBy;
    /**
     *
     */
    @TableField("createdDate")
    private Date createdDate;
    /**
     *
     */
    @TableField("createdVersion")
    private String createdVersion;
    /**
     *
     */

    @TableField("`order`")
    private Integer order;
    /**
     *
     */
    private String deleted;

}
