package com.qmqb.imp.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzed.structure.common.exception.ServiceException;
import com.qmqb.imp.common.constant.CommConstants;
import com.qmqb.imp.common.constant.UserConstants;
import com.qmqb.imp.common.core.domain.dto.CountByUserAndAddedDateDTO;
import com.qmqb.imp.common.core.domain.dto.CountByUserIdAndCreateDateDTO;
import com.qmqb.imp.common.core.domain.dto.SumByComitterAndCommitDateDTO;
import com.qmqb.imp.common.core.domain.entity.SysRole;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.core.domain.model.LoginUser;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.enums.*;
import com.qmqb.imp.common.helper.LoginHelper;
import com.qmqb.imp.common.utils.DateUtils;
import com.qmqb.imp.common.utils.StringUtils;
import com.qmqb.imp.system.domain.bo.*;
import com.qmqb.imp.system.domain.vo.PerformanceDataVO;
import com.qmqb.imp.system.domain.vo.TrackWorkResultVO;
import com.qmqb.imp.system.domain.vo.UserLeaveVo;
import com.qmqb.imp.system.runner.HolidayUtil;
import com.qmqb.imp.system.service.*;
import com.qmqb.imp.system.service.ITrackWorkResultService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.function.ToDoubleFunction;
import java.util.function.ToIntFunction;
import java.util.stream.Collectors;
import java.time.LocalDate;
import java.time.YearMonth;

/**
 * 工作成果跟踪
 * <AUTHOR>
 * @since 2023-08-21
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class TrackWorkResultServiceImpl implements ITrackWorkResultService {

    private final IWorkStatService workStatService;
    private final ISysUserService sysUserService;
    private final IZtDocService ztDocService;
    private final IUserLeaveService userLeaveService;
    private final IGitStatisticsInfoService gitStatisticsInfoService;
    private final IWarnRecordService warnRecordService;
    private final ISysDeptService sysDeptService;
    private final DeptCacheService deptCacheService;

    @Override
    public TableDataInfo<TrackWorkResultVO> list(TrackWorkResultBO request) {
        Page<TrackWorkResultVO> page = new Page<>(request.getPageNum(), request.getPageSize());
        Integer year = request.getEvalYear();
        Integer month = request.getEvalMonth();
        List<Long> groupIdList;

        // 获取当前登录用户信息
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null) {
            throw new ServiceException("当前用户未登录");
        }
        SysUser currentUser = sysUserService.selectUserById(loginUser.getUserId());

        // 权限过滤：如果不是管理员或技术总监或项目经理，只能查看自己所在组的数据
        boolean isLimitedToOwnGroup = false;
        if (currentUser != null && currentUser.getRoles() != null) {
            if (!currentUser.isAdmin() && !currentUser.isJszxAdmin() && !currentUser.isProjectManager()) {
                isLimitedToOwnGroup = true;
            }
        }

        if (isLimitedToOwnGroup) {
            // 仅能查看自己组的数据
            groupIdList = Collections.singletonList(currentUser.getDeptId());
        } else if (ObjUtil.isNull(request.getGroupId()) || UserConstants.JSZX_DEPT_ID.equals(request.getGroupId())) {
            groupIdList = sysDeptService.listTecCenterDeptIdList();
        } else {
            groupIdList = Collections.singletonList(request.getGroupId());
        }

        return getTrackWorkResultVoTableDataInfo(request, page, groupIdList, year, month);
    }

    @Override
    public TableDataInfo<TrackWorkResultVO> getTrackWorkResultVoTableDataInfo(TrackWorkResultBO request, Page<TrackWorkResultVO> page, List<Long> groupIdList, Integer year, Integer month) {
        //以绩效综合统计为主表，再去查询代码行数和预警
        List<TrackWorkResultVO> perfStatList;
        perfStatList = workStatService.getPerfStatList2(page, groupIdList, year, month,
            request.getUserName(), PersonTypeEnum.getRoleIdListFromPostType(request.getPostType()));
        if (CollectionUtil.isEmpty(perfStatList)) {
            log.info("绩效综合统计信息为空");
            return null;
        }
        List<UserLeaveVo> userLeaveVos = userLeaveService.statisticUserLeave(groupIdList, year.toString(), month.toString());

        Map<String, BigDecimal> totalLeaveTime = HolidayUtil.computerTotalLeaveTime(userLeaveVos, year, month);

        TableDataInfo<TrackWorkResultVO> dataInfo = TableDataInfo.build(page);
        dataInfo.setRows(perfStatList);
        //根据用户名查询用户信息
        List<SysUser> sysUsers = sysUserService.selectUserByNickNames(perfStatList.stream().map(TrackWorkResultVO::getWorkUsername).collect(Collectors.toList()));

        List<CountByUserAndAddedDateBO> queryDocList = new ArrayList<>();
        List<SumByComitterAndCommitDateBO> queryGitList = new ArrayList<>();
        List<CountByUserIdAndCreateDateBO> queryWarnList = new ArrayList<>();
        Map<String, SysUser> sysUserMap = sysUsers.stream().collect(Collectors.toMap(SysUser::getNickName, Function.identity()));
        perfStatList.forEach(tmp -> {
            //查询最新组名
            String deptName = deptCacheService.getDeptNameById(tmp.getWorkGroupId());
            tmp.setWorkGroup(deptName);
            //设置考勤时长
            String kqAttendanceWorkTime;
            if (Objects.isNull(tmp.getKqAttendanceWorkTime())) {
                kqAttendanceWorkTime = "0";
            } else {
                kqAttendanceWorkTime = (new BigDecimal(tmp.getKqAttendanceWorkTime()).divide(new BigDecimal(60), 2, RoundingMode.UP)).toString();
            }
            tmp.setKqAttendanceWorkTime(kqAttendanceWorkTime);

            String kqAttendanceDay = Objects.isNull(tmp.getKqAttendanceDays()) ? "0" :
                (new BigDecimal(tmp.getKqAttendanceDays())).toString();
            tmp.setKqAttendanceDays(kqAttendanceDay);

            BigDecimal leaveAndWorkTime = totalLeaveTime.get(tmp.getWorkUsername());

            tmp.setKqLeaveTime(leaveAndWorkTime != null ? leaveAndWorkTime.toString() : "0");
            //组装请求参数
            SysUser tmpSysUser = sysUserMap.get(tmp.getWorkUsername());
            List<String> committer = new ArrayList<>();
            committer.add(tmpSysUser.getGitCommitterName());
            committer.add(tmpSysUser.getGitAuthorName());
            int workMonth = Objects.isNull(tmp.getWorkMonth()) ? 0 : tmp.getWorkMonth();
            Date beginTime = DateUtils.dateToStart(DateUtils.dateTime(DateUtils.YYYY_MM_DD, DateUtils.getFirstDayOfMonth(tmp.getWorkYear(), workMonth)));
            Date endTime = DateUtils.dateToEnd(DateUtils.dateTime(DateUtils.YYYY_MM_DD, DateUtils.getLastDayOfMonth(tmp.getWorkYear(), workMonth)));
            queryDocList.add(CountByUserAndAddedDateBO.builder().userName(tmpSysUser.getZtUserName()).beginTime(beginTime)
                .endTime(endTime).build());
            queryGitList.add(SumByComitterAndCommitDateBO.builder().committer(committer).beginTime(beginTime)
                .endTime(endTime).build());
            queryWarnList.add(CountByUserIdAndCreateDateBO.builder().userId(tmpSysUser.getUserId()).beginTime(beginTime)
                .endTime(endTime).build());
            tmp.setRoleName(tmpSysUser.getRoles().stream().map(SysRole::getRoleName).collect(Collectors.joining(",")));
            tmp.setRoleIds(tmpSysUser.getRoles().stream().map(SysRole::getRoleId).map(String::valueOf).collect(Collectors.joining(",")));
        });
        fillGitAndDocInfo(request, sysUserMap, queryDocList, queryGitList, queryWarnList, perfStatList);
        return dataInfo;
    }

    /**
     * 填充git和文档信息
     *
     * @param request
     * @param sysUserMap
     * @param queryDocList
     * @param queryGitList
     * @param queryWarnList
     * @param perfStatList
     */
    private void fillGitAndDocInfo(TrackWorkResultBO request, Map<String, SysUser> sysUserMap,
                                   List<CountByUserAndAddedDateBO> queryDocList, List<SumByComitterAndCommitDateBO> queryGitList,
                                   List<CountByUserIdAndCreateDateBO> queryWarnList, List<TrackWorkResultVO> perfStatList) {
        //根据用户和时间区间查询文档数
        Integer queryMonth = (request.getEvalMonth() == null || request.getEvalMonth() < 0) ? 0 : request.getEvalMonth();
        List<CountByUserAndAddedDateDTO> countByUserAndAddedDateList = ztDocService.countByUserAndAddedDate(queryDocList, queryMonth);
        Map<String, CountByUserAndAddedDateDTO> countDocMap = new HashMap<>(16);
        if (CollectionUtil.isNotEmpty(countByUserAndAddedDateList)) {
            countDocMap = countByUserAndAddedDateList.stream().filter(Objects::nonNull).filter(tmp -> Objects.nonNull(tmp.getUserName()))
                .collect(Collectors.toMap(tmp -> new StringBuilder(tmp.getUserName()).toString(), Function.identity()));
        }
        //根据用户名和时间查询代码行数
        //todo 这里集合为何返回对象属性为空，需要细究
        List<SumByComitterAndCommitDateDTO> sumByComitterAndCommitDateList = gitStatisticsInfoService.sumByComitterAndCommitDate(queryGitList, queryMonth);
        Map<String, SumByComitterAndCommitDateDTO> sumGitMap = new HashMap<>(16);
        if (CollectionUtil.isNotEmpty(sumByComitterAndCommitDateList)) {
            sumGitMap = sumByComitterAndCommitDateList.stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(dto -> dto.getCommitter().trim().toLowerCase(), Function.identity()));
        }
        //根据用户名和时间区间查询预警数
        List<CountByUserIdAndCreateDateDTO> countByUserIdAndCreateDateList = warnRecordService.countByUserIdAndCreateDate(queryWarnList, queryMonth);
        Map<String, CountByUserIdAndCreateDateDTO> countWarnMap = new HashMap<>(16);
        if (CollectionUtil.isNotEmpty(countByUserIdAndCreateDateList)) {
            countWarnMap = countByUserIdAndCreateDateList.stream().filter(Objects::nonNull).filter(tmp -> Objects.nonNull(tmp.getUserId()))
                .collect(Collectors.toMap(tmp -> new StringBuilder(String.valueOf(tmp.getUserId())).toString(), Function.identity()));
        }
        Map<String, CountByUserAndAddedDateDTO> finalCountDocMap = countDocMap;
        Map<String, SumByComitterAndCommitDateDTO> finalSumGitMap = sumGitMap;
        Map<String, CountByUserIdAndCreateDateDTO> finalCountWarnMap = countWarnMap;
        perfStatList.forEach(tmp -> {
            SysUser tmpSysUser = sysUserMap.get(tmp.getWorkUsername());
            //设置文档数
            CountByUserAndAddedDateDTO tmpDocCount = finalCountDocMap.get(new StringBuilder(tmpSysUser.getZtUserName()).toString());
            if (Objects.nonNull(tmpDocCount)) {
                tmp.setDocCount(tmpDocCount.getDocCounts());
            }
            //设置git提交行数
            //根据git提交人名获取
            SumByComitterAndCommitDateDTO tmpSum = null;
            if (StringUtils.isNotEmpty(tmpSysUser.getGitCommitterName())) {
                tmpSum = finalSumGitMap.get(new StringBuilder(tmpSysUser.getGitCommitterName()).toString().trim().toLowerCase());
            } else if (StringUtils.isNotEmpty(tmpSysUser.getGitAuthorName())) {
                //根据git作者名获取
                tmpSum = finalSumGitMap.get(new StringBuilder(tmpSysUser.getGitAuthorName()).toString().trim().toLowerCase());
            }
            if (Objects.nonNull(tmpSum)) {
                tmp.setPushCodeLines(tmpSum.getTotalLines());
            }
            //设置预警数
            CountByUserIdAndCreateDateDTO tmpWarnCount = finalCountWarnMap.get(new StringBuilder(String.valueOf(tmpSysUser.getUserId())).toString());
            if (Objects.nonNull(tmpWarnCount)) {
                tmp.setWarnCount(tmpWarnCount.getWarnTimes());
            }
        });
    }


    @Override
    public PerformanceDataVO listOpenApi(TrackWorkResultBO request) {
        Page<TrackWorkResultVO> page = new Page<>(request.getPageNum(), request.getPageSize());
        Integer year = request.getEvalYear();
        Integer month = request.getEvalMonth();
        List<Long> groupIdList;
        if (ObjUtil.isNull(request.getGroupId()) || UserConstants.JSZX_DEPT_ID.equals(request.getGroupId())) {
            groupIdList = sysDeptService.listTecCenterDeptIdList();
        } else {
            groupIdList = Collections.singletonList(request.getGroupId());
        }
        PerformanceDataVO performanceData = new PerformanceDataVO();
        //个人绩效数据
        TableDataInfo<TrackWorkResultVO> trackWorkResultVoTableDataInfo = getTrackWorkResultVoTableDataInfo(request, page, groupIdList, year, month);
        if (trackWorkResultVoTableDataInfo.getRows() == null || trackWorkResultVoTableDataInfo.getRows().size() == 0) {
            return performanceData;
        }
        List<TrackWorkResultVO> personTrackWorkResult = trackWorkResultVoTableDataInfo.getRows().stream()
            .filter(item -> !item.getKqAttendanceDays().equals(CommConstants.CommonValStr.ZERO)).collect(Collectors.toList());
        YearMonth targetMonth = YearMonth.of(year, month);
        YearMonth currentMonth = YearMonth.now();
        BigDecimal standardWorkTime;
        if (targetMonth.isAfter(currentMonth)) {
            standardWorkTime = BigDecimal.ZERO;
        } else {
            LocalDate startDate = targetMonth.atDay(1);
            LocalDate endDate = targetMonth.equals(currentMonth) ? LocalDate.now().minusDays(1) : targetMonth.atEndOfMonth();
            int workDays = HolidayUtil.computeWorkDay(startDate, endDate);
            standardWorkTime = BigDecimal.valueOf(workDays).multiply(BigDecimal.valueOf(7));
        }
        for (TrackWorkResultVO vo : personTrackWorkResult) {
            calculatePersonPerformanceMetrics(vo,standardWorkTime);
        }
        performanceData.setPersonTrackWorkResult(personTrackWorkResult);
        //团队绩效数据
        List<PerformanceDataVO.TrackWorkResult> teamTrackWorkResult = calculateTeamPerformance(personTrackWorkResult,standardWorkTime);
        performanceData.setTeamTrackWorkResult(teamTrackWorkResult);
        //岗位绩效数据
        List<PerformanceDataVO.TrackWorkResult> positionTrackWorkResult = calculatePositionPerformance(personTrackWorkResult,standardWorkTime);
        performanceData.setPostTrackWorkResult(positionTrackWorkResult);

        return performanceData;
    }

    /**
     * 计算各项指标的平均值
     */
    private PerformanceDataVO.TrackWorkResult calculateAverages(PerformanceDataVO.TrackWorkResult result,List<TrackWorkResultVO> members, int memberCount) {
        result.setKqAttendanceWorkTime(calculateStringAverage(members, TrackWorkResultVO::getKqAttendanceWorkTime, memberCount));
        result.setWorkResolveBugCount(calculateAverage(members, TrackWorkResultVO::getWorkResolveBugCount, memberCount));
        result.setPushCodeLines(calculateAverage(members, TrackWorkResultVO::getPushCodeLines, memberCount));
        result.setAllWorkTaskCount(calculateAverage(members, TrackWorkResultVO::getAllWorkTaskCount, memberCount));
        result.setDocCount(calculateAverage(members, TrackWorkResultVO::getDocCount, memberCount));
        result.setWarnCount(calculateAverage(members, TrackWorkResultVO::getWarnCount, memberCount));
        result.setWorkOverTenDaysTaskCount(calculateAverage(members, TrackWorkResultVO::getWorkOverTenDaysTaskCount, memberCount));
        result.setCreateBugCount(calculateAverage(members, TrackWorkResultVO::getCreateBugCount, memberCount));
        result.setWorkCloseBugCount(calculateAverage(members, TrackWorkResultVO::getWorkCloseBugCount, memberCount));
        result.setWorkCaseCount(calculateAverage(members, TrackWorkResultVO::getWorkCaseCount, memberCount));
        result.setWorkConsumedTime(calculateDoubleAverage(members, TrackWorkResultVO::getWorkConsumedTime, memberCount));
        return result;
    }

    /**
     * 计算个人绩效指标
     */
    private void calculatePersonPerformanceMetrics(TrackWorkResultVO vo, BigDecimal standardWorkTime) {
        BigDecimal workTime = safeBigDecimal(vo.getKqAttendanceWorkTime());
        // 饱和度
        vo.setSaturation(standardWorkTime.compareTo(BigDecimal.ZERO) == 0 ? "0%" :
                workTime.divide(standardWorkTime, 4, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP) + "%");
        // 任务效率
        vo.setTaskEfficiency(safeDivide(workTime, safeBigDecimal(vo.getAllWorkTaskCount())));
        // 代码效率
        vo.setCodeEfficiency(safeDivide(safeBigDecimal(vo.getPushCodeLines()),workTime));
        // Bug解决率
        vo.setBugSolveEfficiency(safeDivide(safeBigDecimal(vo.getWorkResolveBugCount()), workTime));
        // Bug处理率
        BigDecimal createBugCount = safeBigDecimal(vo.getCreateBugCount());
        BigDecimal closeBugCount = safeBigDecimal(vo.getWorkCloseBugCount());
        vo.setBugHandleRate(createBugCount.compareTo(BigDecimal.ZERO) == 0 ? "0%" :
                closeBugCount.divide(createBugCount, 4, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP) + "%");
        // 执行用例效率
        vo.setCaseEfficiency(safeDivide(workTime, safeBigDecimal(vo.getWorkCaseCount())));
    }

    /**
     * 计算团队绩效指标
     * @param personTrackWorkResult
     * @return
     */
    private List<PerformanceDataVO.TrackWorkResult> calculateTeamPerformance(List<TrackWorkResultVO> personTrackWorkResult,BigDecimal standardWorkTime) {
        Map<Long, List<TrackWorkResultVO>> teamMap = personTrackWorkResult.stream()
            .collect(Collectors.groupingBy(TrackWorkResultVO::getWorkGroupId));
        List<PerformanceDataVO.TrackWorkResult> teamTrackWorkResult = new ArrayList<>();
        for (Map.Entry<Long, List<TrackWorkResultVO>> entry : teamMap.entrySet()) {
            List<TrackWorkResultVO> teamMembers = entry.getValue();
            int memberCount = teamMembers.size();
            PerformanceDataVO.TrackWorkResult teamAvg = new PerformanceDataVO.TrackWorkResult();
            teamAvg.setWorkGroup(teamMembers.get(0).getWorkGroup());
            calculateAverages(teamAvg, teamMembers, memberCount);
            // 禅道耗时占比= 小组成员禅道填写耗时总和/小组成员工作时长总和，结果单位：百分比
            int taskTimes = teamMembers.stream().mapToInt(TrackWorkResultVO::getWorkConsumedTime).sum();
            BigDecimal kpTimes = teamMembers.stream()
                .map(TrackWorkResultVO::getKqAttendanceWorkTime).map(kpTime -> {
                    try {
                        return new BigDecimal(kpTime);
                    } catch (NumberFormatException e) {
                        return BigDecimal.ZERO;
                    }
                }).reduce(BigDecimal.ZERO, BigDecimal::add);
            String workTaskTimePercent = new BigDecimal(taskTimes).divide(kpTimes, 4, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP).toString() + "%";
            teamAvg.setWorkTaskTimePercent(workTaskTimePercent);
            //平均饱和度：人均工作时长/标准⼯作时⻓*100%
            BigDecimal workTime = safeBigDecimal(teamAvg.getKqAttendanceWorkTime());
            teamAvg.setSaturation(standardWorkTime.compareTo(BigDecimal.ZERO) == 0 ? "0%" :
                workTime.divide(standardWorkTime, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP) + "%");
            teamTrackWorkResult.add(teamAvg);
            //任务效率： 人均工作时长 / 人均完成禅道任
            teamAvg.setTaskEfficiency(safeDivide(workTime, safeBigDecimal(teamAvg.getAllWorkTaskCount())));
            //代码效率：人均调整代码行数/ 人均工作时长
            teamAvg.setCodeEfficiency(safeDivide(safeBigDecimal(teamAvg.getPushCodeLines()), workTime));
        }
        return teamTrackWorkResult;
    }

    /**
     * 计算岗位绩效指标
     * @param personTrackWorkResult 个人绩效数据列表
     * @return 岗位绩效数据列表
     */
    private List<PerformanceDataVO.TrackWorkResult> calculatePositionPerformance(List<TrackWorkResultVO> personTrackWorkResult,BigDecimal standardWorkTime) {
        Map<String, List<TrackWorkResultVO>> positionMap = personTrackWorkResult.stream()
            .collect(Collectors.groupingBy(vo -> {
                String roleIds = vo.getRoleIds();
                return Arrays.stream(roleIds.split(","))
                    .max(Comparator.comparingLong(Long::parseLong))
                    .orElse("0");
            }));
        List<PerformanceDataVO.TrackWorkResult> positionTrackWorkResult = new ArrayList<>();

        for (Map.Entry<String, List<TrackWorkResultVO>> entry : positionMap.entrySet()) {
            List<TrackWorkResultVO> positionMembers = entry.getValue();
            int memberCount = positionMembers.size();
            PerformanceDataVO.TrackWorkResult positionAvg = new PerformanceDataVO.TrackWorkResult();
            positionAvg.setRoleName(positionMembers.get(0).getRoleName());
            calculateAverages(positionAvg, positionMembers, memberCount);
            //平均饱和度：人均工作时长/标准⼯作时⻓*100%
            BigDecimal workTime = safeBigDecimal(positionAvg.getKqAttendanceWorkTime());
            positionAvg.setSaturation(standardWorkTime.compareTo(BigDecimal.ZERO) == 0 ? "0%" :
                workTime.divide(standardWorkTime, 4, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP) + "%");
            //任务效率： 人均工作时长 / 人均完成禅道任
            positionAvg.setTaskEfficiency(safeDivide(workTime, safeBigDecimal(positionAvg.getAllWorkTaskCount())));
            //代码效率：人均调整代码行数/ 人均工作时长
            positionAvg.setCodeEfficiency(safeDivide(safeBigDecimal(positionAvg.getPushCodeLines()), workTime));
            positionTrackWorkResult.add(positionAvg);
        }

        return positionTrackWorkResult;
    }

    /**
     * 计算数值类型字段的平均值
     * @param list
     * @param getter
     * @param divisor
     * @return
     */
    private BigDecimal calculateAverage(List<TrackWorkResultVO> list, ToIntFunction<TrackWorkResultVO> getter, int divisor) {
        if (divisor == 0) {
            return BigDecimal.ZERO;
        }
        int sum = list.stream().mapToInt(getter).sum();
        return new BigDecimal(sum).divide(new BigDecimal(divisor), 2, RoundingMode.HALF_UP);
    }

    /**
     * 计算数值类型字段的平均值
     * @return
     */
    private BigDecimal calculateDoubleAverage(List<TrackWorkResultVO> list, ToDoubleFunction<TrackWorkResultVO> getter, int divisor) {
        if (divisor == 0) {
            return BigDecimal.ZERO;
        }
        double sum = list.stream().mapToDouble(getter).sum();
        return new BigDecimal(sum).divide(new BigDecimal(divisor), 2, RoundingMode.HALF_UP);
    }

    /**
     * 计算String类型字段的平均值
     * @param list
     * @param getter
     * @param divisor
     * @return
     */
    private BigDecimal calculateStringAverage(List<TrackWorkResultVO> list, Function<TrackWorkResultVO, String> getter, int divisor) {
        if (divisor == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal sum = list.stream().map(getter).map(timeStr -> {
                try {
                    return new BigDecimal(timeStr);
                } catch (NumberFormatException e) {
                    return BigDecimal.ZERO;
                }
            }).reduce(BigDecimal.ZERO, BigDecimal::add);
        return sum.divide(new BigDecimal(divisor), 2, RoundingMode.HALF_UP);
    }

    private BigDecimal safeBigDecimal(Object val) {
        if (val == null) {
            return BigDecimal.ZERO;
        }
        try {
            if (val instanceof Integer) {
                return new BigDecimal((Integer) val);
            }
            return new BigDecimal(val.toString());
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }
    private BigDecimal safeDivide(BigDecimal a, BigDecimal b) {
        if (b == null || b.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return a.divide(b, 2, RoundingMode.HALF_UP);
    }
}
