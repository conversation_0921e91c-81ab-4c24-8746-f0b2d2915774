package com.qmqb.imp.system.service;

import com.qmqb.imp.system.domain.ProjectTechnology;
import com.qmqb.imp.system.domain.vo.ProjectTechnologyVo;
import com.qmqb.imp.system.domain.bo.ProjectTechnologyBo;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 代码库相关技术栈Service接口
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
public interface IProjectTechnologyService {

    /**
     * 根据主键查询代码库相关技术栈详情
     *
     * @param id 代码库相关技术栈主键
     * @return 代码库相关技术栈详情信息
     */
    ProjectTechnologyVo queryById(Long id);

    /**
     * 查询代码库相关技术栈分页列表
     *
     * @param bo 查询条件参数
     * @param pageQuery 分页参数
     * @return 代码库相关技术栈分页数据
     */
    TableDataInfo<ProjectTechnologyVo> queryPageList(ProjectTechnologyBo bo, PageQuery pageQuery);

    /**
     * 查询代码库相关技术栈列表
     *
     * @param bo 查询条件参数
     * @return 代码库相关技术栈列表数据
     */
    List<ProjectTechnologyVo> queryList(ProjectTechnologyBo bo);

    /**
     * 新增代码库相关技术栈
     *
     * @param bo 代码库相关技术栈业务对象
     * @return 是否新增成功
     */
    Boolean insertByBo(ProjectTechnologyBo bo);

    /**
     * 修改代码库相关技术栈
     *
     * @param bo 代码库相关技术栈业务对象
     * @return 是否修改成功
     */
    Boolean updateByBo(ProjectTechnologyBo bo);

    /**
     * 校验并批量删除代码库相关技术栈信息
     *
     * @param ids 需要删除的代码库相关技术栈主键集合
     * @param isValid 是否进行校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}