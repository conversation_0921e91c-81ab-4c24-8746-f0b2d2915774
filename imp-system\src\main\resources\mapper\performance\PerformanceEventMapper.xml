<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.qmqb.imp.system.mapper.performance.PerformanceEventMapper">

    <resultMap type="com.qmqb.imp.system.domain.performance.PerformanceEvent" id="PerformanceEventResult">
        <result property="id" column="id"/>
        <result property="feedbackCode" column="feedback_code"/>
        <result property="year" column="year"/>
        <result property="month" column="month"/>
        <result property="feedbackTime" column="feedback_time"/>
        <result property="eventTitle" column="event_title"/>
        <result property="eventDetail" column="event_detail"/>
        <result property="eventStartTime" column="event_start_time"/>
        <result property="eventEndTime" column="event_end_time"/>
        <result property="dataSource" column="data_source"/>
        <result property="submitStatus" column="submit_status"/>
        <result property="submitTime" column="submit_time"/>
        <result property="submitter" column="submitter"/>
        <result property="projectManagerAuditStatus" column="project_manager_audit_status"/>
        <result property="finalAudit" column="final_audit"/>
        <result property="remark" column="remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="getMaxMainFeedbackCodeByDatePrefix" resultType="java.lang.String">
        SELECT MAX(feedback_code)
        FROM tb_performance_event
        WHERE feedback_code LIKE CONCAT(#{datePrefix}, '%')
    </select>

    <select id="selectPerformanceDistributionByEventIds" resultType="com.qmqb.imp.system.domain.dto.PerformanceDistributionDTO">
        SELECT
            m.event_id AS eventId,
            SUM(CASE WHEN f.recommended_level = 'S' THEN 1 ELSE 0 END) AS sCount,
            SUM(CASE WHEN f.recommended_level = 'A' THEN 1 ELSE 0 END) AS aCount,
            SUM(CASE WHEN f.recommended_level = 'C' THEN 1 ELSE 0 END) AS cCount,
            SUM(CASE WHEN f.recommended_level = 'D' THEN 1 ELSE 0 END) AS dCount
        FROM tb_performance_feedback_main m
        LEFT JOIN tb_performance_feedback f ON m.id = f.main_feedback_id
        <where>
            <if test="eventIds != null and eventIds.size > 0">
                m.event_id IN
                <foreach collection="eventIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        AND f.is_canceled = 0
        </where>
        GROUP BY m.event_id
    </select>

    <select id="selectEventStatusByEventIds" resultType="com.qmqb.imp.system.domain.dto.PerformanceEventStatusDTO">
        SELECT
            m.event_id AS eventId,
            CASE
                WHEN SUM(m.submit_status = 'PENDING_RESUBMIT') > 0 THEN 'PENDING_RESUBMIT'
                WHEN SUM(m.submit_status = 'NOT_SUBMITTED') = COUNT(1) THEN 'NOT_SUBMITTED'
                WHEN SUM(m.submit_status = 'SUBMITTED') = COUNT(1) THEN 'SUBMITTED'
                ELSE 'PART_SUBMITTED'
            END AS submitStatus,
            CASE
                WHEN SUM(m.project_manager_audit_status = 'NOT_AUDITED') > 0 THEN 'NOT_AUDITED'
                ELSE 'AUDITED'
            END AS projectManagerAuditStatus,
            CASE
                WHEN SUM(m.final_audit = 'NOT_AUDITED') > 0 THEN 'NOT_AUDITED'
                ELSE 'AUDITED'
            END AS finalAudit
        FROM tb_performance_feedback_main m
        <where>
            <if test="eventIds != null and eventIds.size > 0">
                m.event_id IN
                <foreach collection="eventIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        GROUP BY m.event_id
    </select>

    <select id="selectEventPageByCondition" resultType="com.qmqb.imp.system.domain.vo.performance.PerformanceEventVo">
        SELECT
            e.*,
            CASE
                WHEN SUM(CASE WHEN fm.submit_status = 'PENDING_RESUBMIT' THEN 1 ELSE 0 END) > 0 THEN 'PENDING_RESUBMIT'
                WHEN SUM(CASE WHEN fm.submit_status = 'NOT_SUBMITTED' THEN 1 ELSE 0 END) = COUNT(fm.id) AND COUNT(fm.id) > 0 THEN 'NOT_SUBMITTED'
                WHEN SUM(CASE WHEN fm.submit_status = 'SUBMITTED' THEN 1 ELSE 0 END) = COUNT(fm.id) AND COUNT(fm.id) > 0 THEN 'SUBMITTED'
                ELSE 'PART_SUBMITTED'
            END AS submitStatus,
            CASE
                WHEN SUM(CASE WHEN fm.project_manager_audit_status = 'APPROVED' OR fm.project_manager_audit_status = 'REJECTED' THEN 1 ELSE 0 END) = COUNT(fm.id) AND COUNT(fm.id) > 0 THEN 'AUDITED'
                ELSE 'NOT_AUDITED'
            END AS projectManagerAuditStatus,
            CASE
                WHEN SUM(CASE WHEN fm.final_audit = 'APPROVED' OR fm.final_audit = 'REJECTED' THEN 1 ELSE 0 END) = COUNT(fm.id) AND COUNT(fm.id) > 0 THEN 'AUDITED'
                ELSE 'NOT_AUDITED'
            END AS finalAudit
        FROM tb_performance_event e
        LEFT JOIN tb_performance_feedback_main fm ON e.id = fm.event_id
        <where>
            <if test="queryBo.feedbackCode != null and queryBo.feedbackCode != ''">
                AND e.feedback_code = #{queryBo.feedbackCode}
            </if>
            <if test="queryBo.year != null">
                AND e.year = #{queryBo.year}
            </if>
            <if test="queryBo.month != null">
                AND e.month = #{queryBo.month}
            </if>
            <if test="queryBo.eventTitle != null and queryBo.eventTitle != ''">
                AND e.event_title = #{queryBo.eventTitle}
            </if>
            <if test="queryBo.eventDetail != null and queryBo.eventDetail != ''">
                AND e.event_detail = #{queryBo.eventDetail}
            </if>
            <if test="queryBo.eventStartTime != null">
                AND e.event_end_time &gt;= #{queryBo.eventStartTime}
            </if>
            <if test="queryBo.eventEndTime != null">
                AND e.event_end_time &lt;= #{queryBo.eventEndTime}
            </if>
            <if test="queryBo.dataSource != null and queryBo.dataSource != ''">
                AND e.data_source = #{queryBo.dataSource}
            </if>
            <if test="queryBo.submitTimeBegin != null">
                AND e.submit_time &gt;= #{queryBo.submitTimeBegin}
            </if>
            <if test="queryBo.submitTimeEnd != null">
                AND e.submit_time &lt;= #{queryBo.submitTimeEnd}
            </if>
            <if test="queryBo.submitTime != null">
                AND e.submit_time = #{queryBo.submitTime}
            </if>
            <if test="queryBo.feedbackTimeBegin != null">
                AND e.feedback_time &gt;= #{queryBo.feedbackTimeBegin}
            </if>
            <if test="queryBo.feedbackTimeEnd != null">
                AND e.feedback_time &lt;= #{queryBo.feedbackTimeEnd}
            </if>
            <if test="queryBo.feedbackTime != null">
                AND e.feedback_time = #{queryBo.feedbackTime}
            </if>
            <!-- 权限控制：非管理员和技术总监才加限制 -->
            <if test="queryBo.isAdmin == null or !queryBo.isAdmin">
                <if test="queryBo.isJszxAdmin == null or !queryBo.isJszxAdmin">
                    <!-- 项管权限 -->
                    <if test="queryBo.isProjectManager != null and queryBo.isProjectManager">
                        AND (
                            (e.submitter = #{queryBo.userName} OR e.create_by = #{queryBo.userName})
                            OR (e.project_manager_auditor = #{queryBo.userName})
                            OR (fm.secondary_indicator = 'prod_fault')
                            OR (fm.secondary_indicator = 'project_release_count')
                            OR (fm.secondary_indicator = 'dev_spec' AND fm.data_source = 'SYSTEM_GENERATED')
                        )
                    </if>
                    <!-- 普通成员只能查本组 -->
                    <if test="queryBo.isProjectManager == null or !queryBo.isProjectManager">
                        AND (e.submitter = #{queryBo.userName} OR e.create_by = #{queryBo.userName})
                    </if>
                </if>
            </if>
        </where>
        GROUP BY e.id
        HAVING 1=1
        <if test="queryBo.submitStatus != null and queryBo.submitStatus != ''">
            <choose>
                <when test="queryBo.submitStatus == 'PENDING_RESUBMIT'">
                    AND (SUM(CASE WHEN fm.submit_status = 'PENDING_RESUBMIT' THEN 1 ELSE 0 END) > 0)
                </when>
                <when test="queryBo.submitStatus == 'NOT_SUBMITTED'">
                    AND (SUM(CASE WHEN fm.submit_status = 'NOT_SUBMITTED' THEN 1 ELSE 0 END) = COUNT(fm.id) AND COUNT(fm.id) > 0
                    <if test="queryBo.isProjectManager != null and queryBo.isProjectManager">
                        OR (e.submitter = #{queryBo.userName} OR e.create_by = #{queryBo.userName})
                    </if>
                    )
                </when>
                <when test="queryBo.submitStatus == 'SUBMITTED'">
                    AND SUM(CASE WHEN fm.submit_status = 'SUBMITTED' THEN 1 ELSE 0 END) = COUNT(fm.id) AND COUNT(fm.id) > 0
                </when>
            </choose>
        </if>
        <if test="queryBo.submitStatus == null or queryBo.submitStatus == ''">
            <choose>
                <when test="queryBo.isProjectManager != null and queryBo.isProjectManager">
                    AND (SUM(CASE WHEN fm.submit_status = 'SUBMITTED' THEN 1 ELSE 0 END) = COUNT(fm.id) AND COUNT(fm.id) > 0
                    OR  SUM(CASE WHEN fm.submit_status = 'PENDING_RESUBMIT' THEN 1 ELSE 0 END) > 0
                    OR (e.submitter = #{queryBo.userName} OR e.create_by = #{queryBo.userName}))
                </when>
            </choose>
        </if>
        <if test="queryBo.projectManagerAuditStatus != null and queryBo.projectManagerAuditStatus != ''">
            <choose>
                <when test="queryBo.projectManagerAuditStatus == 'AUDITED'">
                    AND SUM(CASE WHEN fm.project_manager_audit_status = 'APPROVED' OR fm.project_manager_audit_status = 'REJECTED' THEN 1 ELSE 0 END) = COUNT(fm.id) AND COUNT(fm.id) > 0
                </when>
                <when test="queryBo.projectManagerAuditStatus == 'NOT_AUDITED'">
                    AND SUM(CASE WHEN fm.project_manager_audit_status = 'APPROVED' OR fm.project_manager_audit_status = 'REJECTED' THEN 1 ELSE 0 END) &lt;&gt; COUNT(fm.id) AND COUNT(fm.id) > 0
                </when>
            </choose>
        </if>
        <if test="queryBo.finalAudit != null and queryBo.finalAudit != ''">
            <choose>
                <when test="queryBo.finalAudit == 'AUDITED'">
                    AND SUM(CASE WHEN fm.final_audit = 'APPROVED' OR fm.final_audit = 'REJECTED' THEN 1 ELSE 0 END) = COUNT(fm.id) AND COUNT(fm.id) > 0
                </when>
                <when test="queryBo.finalAudit == 'NOT_AUDITED'">
                    AND SUM(CASE WHEN fm.final_audit = 'APPROVED' OR fm.final_audit = 'REJECTED' THEN 1 ELSE 0 END) &lt;&gt; COUNT(fm.id) AND COUNT(fm.id) > 0
                </when>
            </choose>
        </if>
    </select>

</mapper>
