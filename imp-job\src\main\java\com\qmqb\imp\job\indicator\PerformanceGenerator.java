package com.qmqb.imp.job.indicator;

import com.qmqb.imp.common.core.domain.entity.SysDept;
import com.qmqb.imp.common.core.domain.entity.SysRole;
import com.qmqb.imp.common.core.domain.entity.SysUser;
import com.qmqb.imp.common.enums.IndicatorCategoryEnum;
import com.qmqb.imp.common.enums.PerformanceIndicatorEnum;
import com.qmqb.imp.common.enums.PersonTypeEnum;
import com.qmqb.imp.common.enums.ScoreLevelEnum;
import com.qmqb.imp.common.utils.ScoreLevelUtil;
import com.qmqb.imp.system.domain.performance.Performance;
import com.qmqb.imp.system.domain.performance.PerformanceFeedback;
import com.qmqb.imp.system.domain.performance.PerformanceIndicator;
import com.qmqb.imp.system.domain.performance.PerformanceIndicatorCategory;
import com.qmqb.imp.system.service.ISysUserService;
import com.qmqb.imp.system.service.indicator.IPerformanceFeedbackService;
import com.qmqb.imp.system.service.indicator.IPerformanceIndicatorCategoryService;
import com.qmqb.imp.system.service.indicator.IPerformanceIndicatorService;
import com.qmqb.imp.system.service.indicator.IPerformanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 绩效生成器
 * 根据绩效反馈记录生成绩效主表和相关记录
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class PerformanceGenerator {

    @Autowired
    private IPerformanceService performanceService;

    @Autowired
    private IPerformanceIndicatorService performanceIndicatorService;

    @Autowired
    private IPerformanceIndicatorCategoryService performanceIndicatorCategoryService;

    @Autowired
    private IPerformanceFeedbackService performanceFeedbackService;

    @Autowired
    private ISysUserService sysUserService;

    /**
     * 根据绩效反馈记录生成绩效主表（支持部门过滤）
     *
     * @param year       年份
     * @param month      月份
     * @param personType 表示当前逻辑是跑哪个人员类型 0非组长，1组长
     * @param deptId     部门ID，如果为null则处理所有部门
     */
    @Transactional(rollbackFor = Exception.class)
    public void generatePerformanceFromFeedbackAndDept(Integer year, Integer month, Integer personType, Long deptId) {

        // 0. 根据角色类型删除该年月的旧绩效记录
        performanceService.removeByYearAndMonth(year, month, personType, deptId);

        // 1. 获取该年月的所有绩效反馈记录
        List<PerformanceFeedback> feedbackList = performanceFeedbackService.getByYearAndMonth(year, month);

        // 2. 如果指定了部门ID，过滤只保留该部门的反馈记录
        if (deptId != null) {
            feedbackList = feedbackList.stream()
                .filter(feedback -> deptId.equals(feedback.getGroupId()))
                .collect(Collectors.toList());
        }

        // 3. 按员工分组反馈记录
        Map<String, List<PerformanceFeedback>> employeeFeedbackMap = feedbackList.stream()
                .collect(Collectors.groupingBy(PerformanceFeedback::getNickName));

        // 4. 为每个员工生成绩效记录
        for (Map.Entry<String, List<PerformanceFeedback>> entry : employeeFeedbackMap.entrySet()) {
            String nickName = entry.getKey();
            List<PerformanceFeedback> employeeFeedbacks = entry.getValue();

            generatePerformanceForEmployee(nickName, year, month, employeeFeedbacks, personType);
        }
    }

    /**
     * 为员工生成绩效记录（内部方法）
     * 支持每项指标有多个反馈的情况，使用ScoreLevelUtil#determineCategoryLevel计算综合等级
     */
    private void generatePerformanceForEmployee(String nickName, Integer year, Integer month,
                                                List<PerformanceFeedback> feedbackList, Integer personType) {
        // 1. 获取用户信息
        SysUser sysUser = sysUserService.selectUserByNickName(nickName);
        if (sysUser == null) {
            throw new RuntimeException("未找到员工[" + nickName + "]的用户信息");
        }
        SysDept dept = sysUser.getDept();
        List<SysRole> roles = sysUser.getRoles();
        String roleName = roles.stream().findFirst().map(SysRole::getRoleName).orElse(null);
        // 2. 确定角色类型
        PersonTypeEnum personTypeEnum = null;
        if (!feedbackList.isEmpty()) {
            String personTypeStr = feedbackList.get(0).getPersonType();
            try {
                personTypeEnum = PersonTypeEnum.fromType(Integer.valueOf(personTypeStr));
            } catch (IllegalArgumentException e) {
                log.info("角色类型非法");
            }
        }
        if (personTypeEnum == null) {
            throw new RuntimeException("无法确定员工[" + nickName + "]的角色类型");
        }
        //处理组长数据，非组长的数据跳过
        if (personType == 1 && !PersonTypeEnum.TECHNICAL_MANAGER.getType().equals(personTypeEnum.getType())) {
            return;
        }
        //如果跑非组长的数据，则跳过组长的数据
        if (personType == 0 && PersonTypeEnum.TECHNICAL_MANAGER.getType().equals(personTypeEnum.getType())) {
            return;
        }
        // 3. 创建绩效主表记录
        Performance performance = new Performance();
        performance.setNickName(nickName);
        performance.setMonth(month);
        performance.setYear(year);
        performance.setRole(roleName);
        performance.setRoleId(Long.valueOf(personTypeEnum.getType()));
        performance.setGroupId(dept.getDeptId());
        performance.setGroupName(dept.getDeptName());
        // 保存后才能拿到id
        performanceService.save(performance);
        // 4. 按指标编码分组反馈记录，处理每项指标可能有多个反馈的情况
        Map<String, List<PerformanceFeedback>> indicatorFeedbackMap = feedbackList.stream()
                .collect(Collectors.groupingBy(PerformanceFeedback::getSecondaryIndicator));
        List<PerformanceIndicator> indicators = new ArrayList<>();
        for (Map.Entry<String, List<PerformanceFeedback>> entry : indicatorFeedbackMap.entrySet()) {
            String indicatorCode = entry.getKey();
            List<PerformanceFeedback> indicatorFeedbacks = entry.getValue();
            // 计算该指标的综合等级
            String compositeLevel = calculateCompositeLevel(indicatorFeedbacks);
            // 合并该指标的所有事件详情
            String compositeLogContent = mergeEventDetails(indicatorFeedbacks);
            // 创建指标记录
            PerformanceIndicator indicator = new PerformanceIndicator();
            indicator.setPerformanceId(performance.getId());
            indicator.setIndicatorCode(indicatorCode);
            indicator.setIndicatorName(PerformanceIndicatorEnum.fromCode(indicatorCode).getName());
            indicator.setScoreLevel(compositeLevel);
            indicator.setLogContent(compositeLogContent);
            // 设置指标分类编码
            IndicatorCategoryEnum category = IndicatorCategoryEnum.getByIndicatorCode(indicator.getIndicatorCode());
            if (category != null) {
                indicator.setCategoryCode(category.getCode());
            }
            indicators.add(indicator);
        }
        // 5. 保存指标记录
        performanceIndicatorService.saveBatch(indicators);
        // 6. 计算各分类等级
        List<PerformanceIndicatorCategory> categories = calculateCategoryLevels(indicators, performance.getId(), personTypeEnum);
        // 7. 基于分类计算总评等级
        String totalLevel = performanceService.calculateTotalLevelByCategories(categories);
        performance.setTotalLevel(totalLevel);
        performanceService.updateById(performance);
    }

    /**
     * 计算指标的综合等级
     * 使用ScoreLevelUtil#determineCategoryLevel方法，将多个反馈的等级转换为A级数量后计算综合等级
     *
     * @param feedbacks 同一指标的多个反馈记录
     * @return 综合等级
     */
    private String calculateCompositeLevel(List<PerformanceFeedback> feedbacks) {
        if (feedbacks == null || feedbacks.isEmpty()) {
            return ScoreLevelEnum.SCORE_C.getCode();
        }

        // 计算A级数量和C级数量
        double totalA = 0.0;
        int cCount = 0;

        for (PerformanceFeedback feedback : feedbacks) {
            String level = feedback.getRecommendedLevel();
            double aValue = ScoreLevelUtil.convertLevelToAvalue(level);
            totalA += aValue;

            // 有D，则为D
            if (ScoreLevelEnum.SCORE_D.getCode().equals(level)) {
                return feedback.getRecommendedLevel();
            }

            if (ScoreLevelEnum.SCORE_C.getCode().equals(level)) {
                cCount++;
            }
        }
        // 获取全部levels
        List<String> levels = feedbacks.stream()
                .map(PerformanceFeedback::getRecommendedLevel)
                .distinct().collect(Collectors.toList());

        // 使用ScoreLevelUtil#determineCategoryLevel计算综合等级
        return ScoreLevelUtil.determineFeedBackCategoryLevel(totalA, cCount,levels);
    }

    /**
     * 合并多个反馈的事件详情
     *
     * @param feedbacks 反馈记录列表
     * @return 合并后的事件详情
     */
    private String mergeEventDetails(List<PerformanceFeedback> feedbacks) {
        if (feedbacks == null || feedbacks.isEmpty()) {
            return "";
        }

        // 如果只有一个反馈，直接返回其事件详情
        if (feedbacks.size() == 1) {
            return feedbacks.get(0).getEventDetail();
        }

        // 合并多个反馈的事件详情
        StringBuilder compositeLog = new StringBuilder();
        compositeLog.append("综合多个反馈记录：\n");

        for (int i = 0; i < feedbacks.size(); i++) {
            PerformanceFeedback feedback = feedbacks.get(i);
            compositeLog.append(i + 1).append(". ")
                    .append(feedback.getEventTitle())
                    .append("：")
                    .append(feedback.getEventDetail())
                    .append("（等级：").append(feedback.getRecommendedLevel()).append("）\n");
        }

        return compositeLog.toString();
    }

    /**
     * 计算各分类等级
     */
    private List<PerformanceIndicatorCategory> calculateCategoryLevels(List<PerformanceIndicator> indicators, Long performanceId, PersonTypeEnum personTypeEnum) {
        List<PerformanceIndicatorCategory> categories = new ArrayList<>();

        // 按分类分组指标
        Map<String, List<PerformanceIndicator>> categoryIndicatorMap = indicators.stream()
                .filter(indicator -> indicator.getCategoryCode() != null)
                .collect(Collectors.groupingBy(PerformanceIndicator::getCategoryCode));

        // 计算每个分类的等级
        for (IndicatorCategoryEnum categoryEnum : IndicatorCategoryEnum.values()) {
            String categoryCode = categoryEnum.getCode();
            List<PerformanceIndicator> categoryIndicators = categoryIndicatorMap.get(categoryCode);

            PerformanceIndicatorCategory category = performanceIndicatorCategoryService.calculateCategoryLevel(
                    categoryIndicators, categoryCode, categoryEnum.getName(), null);

            category.setPerformanceId(performanceId);
            categories.add(category);
        }

        // 保存分类记录
        performanceIndicatorCategoryService.saveBatch(categories);

        return categories;
    }
}
