package com.qmqb.imp.system.domain.vo.performance;

import java.time.LocalDate;
import java.util.Date;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


/**
 * 绩效辅导视图对象 tb_performance_tutoring
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@ExcelIgnoreUnannotated
public class PerformanceTutoringVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 绩效反馈表id(tb_performance_feedback)
     */
    @ExcelProperty(value = "绩效反馈表id(tb_performance_feedback)")
    private Long feedbackId;

    /**
     * 辅导概要
     */
    @ExcelProperty(value = "辅导概要")
    private String tutoringSummary;

    /**
     * 辅导结果
     */
    @ExcelProperty(value = "辅导结果")
    private String tutoringResult;

    /**
     * 辅导人
     */
    @ExcelProperty(value = "辅导人")
    private String tutor;

    /**
     * 辅导时间
     */
    @ExcelProperty(value = "辅导时间")
    private LocalDate tutoringTime;

    /**
     * 辅导附件
     */
    @ExcelProperty(value = "辅导附件")
    private String tutoringAttachment;

    /**
     * 总监建议
     */
    @ExcelProperty(value = "总监建议")
    private String directorSuggest;

    /**
     * 建议时间
     */
    @ExcelProperty(value = "建议时间")
    private Date suggestTime;

    /**
     * 反馈编码
     */
    private String feedbackCode;

    /**
     * 一类指标
     */
    private String primaryIndicator;

    private String primaryIndicatorName;

    /**
     * 二类指标
     */
    private String secondaryIndicator;

    private String secondaryIndicatorName;

    /**
     * 事件标题
     */
    private String eventTitle;

    /**
     * 事件发生开始时间
     */
    @ExcelProperty(value = "事件发生开始时间")
    private Date eventStartTime;

    /**
     * 事件发生结束时间
     */
    @ExcelProperty(value = "事件发生结束时间")
    private Date eventEndTime;

    /**
     * 推荐绩效级别
     */
    @ExcelProperty(value = "推荐绩效级别")
    private String recommendedLevel;

    /**
     * 所属组
     */
    @ExcelProperty(value = "所属组")
    private String groupName;

    /**
     * 员工昵称
     */
    @ExcelProperty(value = "员工昵称")
    private String nickName;

    /**
     * 岗位名称
     */
    private String roleName;


    /**
     * 事件明细
     */
    private String eventDetail;

    /**
     *
     * 推荐原因
     */
    private String recommendedReason;


}
