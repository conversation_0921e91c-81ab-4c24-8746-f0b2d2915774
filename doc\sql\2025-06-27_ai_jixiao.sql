DROP TABLE IF EXISTS `tb_performance_report`;
CREATE TABLE `tb_performance_report` (
    `id` bigint(20) NOT NULL COMMENT 'id',
    `year` varchar(20) NOT NULL COMMENT '年份',
    `month` varchar(20) NOT NULL COMMENT '月份',
    `report_type` char(2) DEFAULT NULL COMMENT '报告类型 （0-个人 1-小组 2-岗位）',
    `user_name` varchar(20) DEFAULT NULL COMMENT '个人名称',
    `group_name` varchar(30) DEFAULT NULL COMMENT '小组名称',
    `post_name` varchar(30) DEFAULT NULL COMMENT '岗位名称',
    `report_url` varchar(255) DEFAULT NULL COMMENT '报告地址',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='绩效分析报告';


-- 新增AI绩效报告查询菜单
INSERT INTO `sys_menu` (`menu_id`, `menu_name`, `parent_id`, `order_num`, `path`, `component`, `query_param`, `is_frame`, `is_cache`, `menu_type`, `visible`, `status`, `perms`, `icon`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (1948570951451533313, 'AI绩效报告查询', 1600327964042485761, 14, 'AIPerformanceReport', 'business/AIPerformanceReport/index', NULL, 1, 0, 'C', '0', '0', 'system:performanceReport:list', 'chart', 'admin', '2025-07-25 10:28:12', 'admin', '2025-07-25 14:22:25', '');

-- 字典数据
INSERT INTO `sys_dict_type` (`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (1935167154448515074, 'IP白名单（API调用）', 'ip_white_list', '0', 'admin', '2025-06-18 10:46:18', 'admin', '2025-06-18 10:46:18', NULL);

INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (1956193942997905409, 0, 'Chat Master管理系统1', '*************', 'ip_white_list', NULL, 'default', 'N', '0', 'admin', '2025-08-15 11:19:15', 'admin', '2025-08-15 11:20:39', NULL);
INSERT INTO `sys_dict_data` (`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES (1956194263413370881, 1, 'Chat Master管理系统2', '**************', 'ip_white_list', NULL, 'default', 'N', '0', 'admin', '2025-08-15 11:20:31', 'admin', '2025-08-15 11:20:31', NULL);
