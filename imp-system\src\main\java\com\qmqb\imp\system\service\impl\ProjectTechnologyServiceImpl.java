package com.qmqb.imp.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.qmqb.imp.common.core.page.TableDataInfo;
import com.qmqb.imp.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.qmqb.imp.system.mapper.ProjectTechnologyMapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.qmqb.imp.system.domain.bo.ProjectTechnologyBo;
import com.qmqb.imp.system.domain.vo.ProjectTechnologyVo;
import com.qmqb.imp.system.domain.ProjectTechnology;
import com.qmqb.imp.system.service.IProjectTechnologyService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 代码库相关技术栈Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-14
 */
@RequiredArgsConstructor
@Service
public class ProjectTechnologyServiceImpl implements IProjectTechnologyService {

    private final ProjectTechnologyMapper baseMapper;

    /**
     * 查询代码库相关技术栈
     */
    @Override
    public ProjectTechnologyVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询代码库相关技术栈列表
     */
    @Override
    public TableDataInfo<ProjectTechnologyVo> queryPageList(ProjectTechnologyBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProjectTechnology> lqw = buildQueryWrapper(bo);
        Page<ProjectTechnologyVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询代码库相关技术栈列表
     */
    @Override
    public List<ProjectTechnologyVo> queryList(ProjectTechnologyBo bo) {
        LambdaQueryWrapper<ProjectTechnology> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProjectTechnology> buildQueryWrapper(ProjectTechnologyBo bo) {
        LambdaQueryWrapper<ProjectTechnology> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getPId() != null, ProjectTechnology::getPId, bo.getPId());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), ProjectTechnology::getType, bo.getType());
        return lqw;
    }

    /**
     * 新增代码库相关技术栈
     */
    @Override
    public Boolean insertByBo(ProjectTechnologyBo bo) {
        ProjectTechnology add = BeanUtil.toBean(bo, ProjectTechnology.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改代码库相关技术栈
     */
    @Override
    public Boolean updateByBo(ProjectTechnologyBo bo) {
        ProjectTechnology update = BeanUtil.toBean(bo, ProjectTechnology.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProjectTechnology entity){
    }

    /**
     * 批量删除代码库相关技术栈
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
